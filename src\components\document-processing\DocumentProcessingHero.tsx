import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, FileText, Zap, Target, Play } from "lucide-react";
import { useRef } from "react";
import { useNavigate } from 'react-router-dom';

const DocumentProcessingHero = () => {
  // Ref for the video section
  const videoSectionRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const handleWatchDemo = () => {
    // Navigate to home page
    navigate('/');
    
    // After navigation, scroll to video section
    setTimeout(() => {
      const videoSection = document.getElementById("ai-demo-video");
      if (videoSection) {
        videoSection.scrollIntoView({ behavior: "smooth" });
        
        // Auto-play video after scrolling
        setTimeout(() => {
          const video = document.getElementById("demo-video") as HTMLVideoElement | null;
          if (video) {
            video.play?.();
          }
        }, 1000);
      }
    }, 100);
  };

  return (
    <section className="py-20 px-6">
      <div className="container mx-auto text-center">
        {/* Top pill with icon and label */}
        <div className="flex justify-center mb-6">
          <div className="flex items-center space-x-2 bg-gradient-to-r from-teal-100 to-green-100 text-teal-700 px-4 py-2 rounded-full text-sm">
            <FileText className="w-4 h-4" />
            <span>Document Processing AI Platform</span>
          </div>
        </div>

        {/* Main heading */}
        <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
          Intelligent Document Processing
          <br />
          <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
            for Every Business
          </span>
        </h1>

        {/* Subheading */}
        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          Transform your operations with AI-powered document extraction,
          classification, and workflow automation. Reduce manual effort, improve
          accuracy, and accelerate your business processes.
        </p>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            size="lg"
            className="group bg-gradient-to-r from-teal-600 to-green-600 text-white hover:from-teal-700 hover:to-green-700"
            asChild
          >
            <a
              href="https://app.docsynecx.com/signin/"
              target="_blank"
              rel="noopener noreferrer"
            >
              Start Free Trial
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </a>
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="border-teal-600 text-teal-700 hover:bg-teal-50 hover:text-teal-900"
            onClick={handleWatchDemo}
          >
            <Play className="w-4 h-4 mr-2" />
            Watch Demo
          </Button>
        </div>

        {/* Feature Icons */}
        <div className="grid md:grid-cols-3 gap-8 mt-16 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <FileText className="w-6 h-6 text-teal-600" />
            </div>
            <h3 className="font-semibold text-lg mb-2">Smart Extraction</h3>
            <p className="text-gray-600">
              AI-powered data extraction from any document format
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Zap className="w-6 h-6 text-green-700" />
            </div>
            <h3 className="font-semibold text-lg mb-2">Real-time Processing</h3>
            <p className="text-gray-600">
              Process thousands of documents in minutes, not hours
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-teal-100 to-green-50 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Target className="w-6 h-6 text-teal-600" />
            </div>
            <h3 className="font-semibold text-lg mb-2">97.0% Accuracy</h3>
            <p className="text-gray-600">
              Industry-leading accuracy with continuous learning
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DocumentProcessingHero;