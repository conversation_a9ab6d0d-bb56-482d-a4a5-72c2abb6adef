import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle } from "lucide-react";

const LogisticsTradeCTA = () => {
  const stats = [
    { value: "500M+", label: "Documents Processed" },
    { value: "97.0%", label: "Average Accuracy" },
    { value: "85%", label: "Cost Reduction" }
  ];

  const benefits = [
    "Reduce manual data entry by 90%",
    "Accelerate document processing by 10x",
    "Eliminate compliance errors",
    "24/7 automated processing"
  ];

  const handleViewDemo = () => {
    const demoForm = document.getElementById('demo-request-form');
    if (demoForm) {
      demoForm.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="py-8 md:py-12 bg-gradient-to-r from-teal-600 to-green-600">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center text-white">
          <h2 className="text-3xl md:text-4xl font-bold mb-3 md:mb-4">
            Transform Your Logistics Operations Today
          </h2>
          <p className="text-lg md:text-xl text-white/90 mb-4 md:mb-6 max-w-3xl mx-auto">
            Join leading logistics companies that have automated their document processing 
            and reduced operational costs while improving accuracy and compliance.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-6 md:mb-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white mb-1 md:mb-2">{stat.value}</div>
                <div className="text-white/80 text-sm md:text-base">{stat.label}</div>
              </div>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-8 md:mb-10 max-w-4xl mx-auto">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center text-left">
                <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-green-300 mr-2 md:mr-3 flex-shrink-0" />
                <span className="text-white/90 text-sm md:text-base">{benefit}</span>
              </div>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="text-lg px-8 bg-white text-teal-700 hover:bg-green-50 border-none"
              onClick={() => window.open('https://app.docsynecx.com/signin/', '_blank')}
            >
              Start Free Trial
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="text-lg px-8 border-2 border-white text-white bg-transparent hover:bg-white hover:text-teal-700"
              onClick={handleViewDemo}
            >
              Schedule Demo
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LogisticsTradeCTA;