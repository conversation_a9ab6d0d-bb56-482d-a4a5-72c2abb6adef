import { useState } from "react";
import { ArrowR<PERSON>, FileText, CheckCircle, Clock, DollarSign, TrendingUp, Users, Check } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import InteractiveKeyValueList from "../components/InteractiveKeyValueList";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useEffect } from "react";
// Invoice data for the interactive demo - Updated to match SLEEK BILL invoice


const invoiceData = {
  "Basic Information": {
    "Invoice Number": "X-7",
    "Issue Date": "21/06/2018",
    "Due Date": "28/06/2018",
    "Place of Supply": "Outside India"
  },
  "Seller Detail": {
    "Name": "SC Alssac SRL",
    "Address": "Pune, JK [01], IN",
    "Phone": "+40744424757",
    "Email": "<EMAIL>",
    "GSTIN": "19AAXHS7580J1Z5"
  },
  "Buyer Detail": {
    "Name": "Test Client US",
    "Address": "Street White 3458, Detroit, MI, US",
    "Phone": "<EMAIL> 03407228018896"
  },
  "Shipping Detail": {
    "Name": "Vic 032947436263",
    "Address": "Street White 3458, Detroit, MI, US",
    "Shipping Port Code": "IN116 (Outside India)",
    "Shipping Bill No": "2348",
    "Shipping Date": "25/06/2018"
  },
  "Products": {
    "Item Description": "Mouse 23, 44mm, optical, black",
    "HSN/SAC": "1110374",
    "Quantity": "25 Pc",
    "Price": "₹3,600.00",
    "Amount": "₹90,000.00"
  },
  "Tax Details": {
    "Taxable Value": "₹90,000.00",
    "IGST": "₹10,800.00 (12%)",
    "Total Tax": "₹10,800.00"
  },
  "Total Amount": {
    "Total Value (INR)": "₹91,200.00",
    "Total Value (USD)": "$1,337",
    "Amount in Words": "Ninety-one Thousand Two Hundred Only"
  }
};

const invoiceJsonData = [
  {
    "invoice_details": {
      "invoice_number": "X-7",
      "invoice_date": "21/06/2018",
      "due_date": "28/06/2018",
      "place_of_supply": "Outside India",
      "currency": "USD",
      "conversion_rate": 68.23
    },
    "seller_details": {
      "company_name": "SC Alssac SRL",
      "address": "Pune, JK [01], IN",
      "phone": "+40744424757",
      "email": "<EMAIL>",
      "gstin": "19AAXHS7580J1Z5"
    },
    "buyer_details": {
      "company_name": "Test Client US",
      "address": "Street White 3458, Detroit, MI, US",
      "contact": "<EMAIL> 03407228018896"
    },
    "shipping_details": {
      "name": "Vic 032947436263",
      "address": "Street White 3458, Detroit, MI, US",
      "port_code": "IN116 (Outside India)",
      "bill_number": "2348",
      "shipping_date": "25/06/2018"
    },
    "line_items": [
      {
        "description": "Mouse 23, 44mm, optical, black",
        "hsn_code": "1110374",
        "quantity": 25,
        "unit": "Pc",
        "rate": 3600.00,
        "taxable_value": 90000.00,
        "igst_rate": "12%",
        "igst_amount": 10800.00,
        "total_amount": 100800.00
      },
      {
        "description": "Shipping & Packaging charges",
        "rate": 1200.00,
        "igst_rate": "0%",
        "igst_amount": 0.00,
        "total_amount": 1200.00
      }
    ],
    "tax_details": {
      "taxable_amount": 90000.00,
      "igst_amount": 10800.00,
      "total_tax": 10800.00
    },
    "totals": {
      "subtotal": 90000.00,
      "total_tax": 10800.00,
      "grand_total_inr": 91200.00,
      "grand_total_usd": 1337.00,
      "amount_in_words": "Ninety-one Thousand Two Hundred Only"
    },
    "payment_details": {
      "bank_name": "test",
      "account_number": "***************",
      "branch_name": "tatert",
      "ifsc_code": "46364"
    }
  }
];

const InvoiceProcessing = () => {

   useEffect(() => {
    document.title = "Automated Invoice Processing Software for Enterprises - DocSynecX";
  }, []);

  const navigate = useNavigate();

  const [showJson, setShowJson] = useState(false);
  const [selectedField, setSelectedField] = useState<string | null>(null);
  
  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    basicInfo: true,
    sellerDetail: true,
    buyerDetail: true,
    shippingDetail: true,
    products: true,
    taxDetails: true,
    totalAmount: true
  });



  const benefits = [
    { icon: Clock, title: "90% Time Reduction", description: "From hours to seconds per invoice", value: "90%" },
    { icon: DollarSign, title: "Cost Savings", description: "Reduce processing costs significantly", value: "$50K+" },
    { icon: CheckCircle, title: "Accuracy Rate", description: "Industry-leading accuracy", value: "97.0%" },
    { icon: Users, title: "Team Efficiency", description: "Free up staff for strategic work", value: "5x" }
  ];

  const handleRemoveSection = (sectionKey: keyof typeof visibleSections) => {
    setVisibleSections(prev => ({
      ...prev,
      [sectionKey]: false
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100">
      <Navbar />

      {/* Hero Section */}
      <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Transform Your{" "}
                <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
                  Invoice Processing
                </span>{" "}
                with AI
              </h1>
              <p className="text-lg sm:text-xl text-gray-600 mb-8 leading-relaxed">
                Automate invoice data extraction with 97.0% accuracy. Process hundreds of invoices in minutes, 
                not hours. Seamlessly integrate with your existing ERP and accounting systems.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 px-8 py-4 text-lg"
                  onClick={() => window.open('https://app.docsynecx.com/signin/', '_blank')}
                >
                  Start Free Trial
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
                <Button size="lg" variant="outline" className="px-8 py-4 text-lg" onClick={() => {
                  const form = document.getElementById('demo-request-form');
                  if (form) form.scrollIntoView({ behavior: 'smooth' });
                }}>
                  Book Demo
                </Button>
              </div>
            </div>

            {/* Animated Invoice Preview */}
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8 transform hover:scale-105 transition-all duration-500">
                <div className="flex items-center mb-6">
                  <FileText className="w-8 h-8 text-teal-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">Sample Invoice</h3>
                </div>
                
                {/* Animated Data Extraction */}
                <div className="space-y-4">
                  {[
                    { label: "Invoice Number", value: "INV-2024-001", delay: "0s" },
                    { label: "Date", value: "March 15, 2024", delay: "0.5s" },
                    { label: "Amount", value: "$1,250.00", delay: "1s" },
                    { label: "Vendor", value: "ABC Company Ltd", delay: "1.5s" }
                  ].map((field, index) => (
                    <div 
                      key={index}
                      className="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse"
                      style={{ animationDelay: field.delay, animationDuration: "2s" }}
                    >
                      <span className="text-gray-600">{field.label}:</span>
                      <span className="font-semibold text-gray-900">{field.value}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-6 flex items-center justify-center">
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    <span className="font-medium">Data Extracted Successfully</span>
                  </div>
                </div>
              </div>

              {/* Floating Elements removed as requested */}
            </div>
          </div>
        </div>
      </section>

      {/* Key Benefits - Simplified */}
      <section className="bg-white py-16 lg:py-20">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose DocSynecX?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Proven results that transform your business
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;

              return (
                <div key={index} className="text-center group">
                  <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{benefit.value}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{benefit.title}</h3>
                  <p className="text-gray-600 text-sm">{benefit.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Interactive Demo Section */}
      <section className="bg-gradient-to-br from-slate-50 via-teal-50 to-green-100 py-16 lg:py-20">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              See Invoice Data Extraction in Action
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how DocSynecX extracts data from invoices in real-time
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-10 max-w-6xl mx-auto">
            {/* Left: Invoice Image */}
            <div className="bg-white rounded-2xl shadow-lg p-6 min-h-[600px] max-h-[600px] relative overflow-hidden flex items-center justify-center">
              <div className="w-full h-full relative">
                <img 
                  src="/invoice.jpeg" 
                  alt="Sample Invoice Document" 
                  className="w-full h-full object-contain rounded-lg shadow-sm border border-gray-200"
                  style={{ maxHeight: '100%', maxWidth: '100%' }}
                />
                {/* Floating accuracy and processing time badges removed as requested */}
              </div>
            </div>

            {/* Right: Extracted Data */}
            <div className="bg-white rounded-2xl shadow-lg p-0 min-h-[600px] max-h-[600px] flex flex-col relative">
              {/* Toggle Buttons */}
              <div className="absolute top-4 right-4 z-20 flex gap-2">
                <button
                  className={`px-3 py-1 text-sm font-medium rounded shadow border transition ${
                    !showJson 
                      ? 'bg-teal-600 text-white border-teal-600' 
                      : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'
                  }`}
                  onClick={() => setShowJson(false)}
                >
                  List View
                </button>
                <button
                  className={`px-3 py-1 text-sm font-medium rounded shadow border transition ${
                    showJson 
                      ? 'bg-teal-600 text-white border-teal-600' 
                      : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'
                  }`}
                  onClick={() => setShowJson(true)}
                >
                  JSON View
                </button>
              </div>

              <div className="flex-1 overflow-y-auto p-8">
                {showJson ? (
                  <div>
                    <div className="mb-4 font-bold text-lg text-gray-700">JSON Data</div>
                    <pre className="bg-white text-green-600 rounded-lg p-4 text-xs sm:text-sm overflow-x-auto max-h-[60vh] whitespace-pre-wrap border border-gray-200">
                      {JSON.stringify(invoiceJsonData, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <>
                    {visibleSections.basicInfo && (
                      <InteractiveKeyValueList
                        data={invoiceData["Basic Information"]}
                        heading="Basic Information"
                        selectedField={selectedField}
                        onFieldClick={setSelectedField}
                      />
                    )}
                    {visibleSections.sellerDetail && (
                      <InteractiveKeyValueList
                        data={invoiceData["Seller Detail"]}
                        heading="Seller Detail"
                        selectedField={selectedField}
                        onFieldClick={setSelectedField}
                      />
                    )}
                    {visibleSections.buyerDetail && (
                      <InteractiveKeyValueList
                        data={invoiceData["Buyer Detail"]}
                        heading="Buyer Detail"
                        selectedField={selectedField}
                        onFieldClick={setSelectedField}
                      />
                    )}
                    {visibleSections.shippingDetail && (
                      <InteractiveKeyValueList
                        data={invoiceData["Shipping Detail"]}
                        heading="Shipping Detail"
                        selectedField={selectedField}
                        onFieldClick={setSelectedField}
                      />
                    )}
                    {visibleSections.products && (
                      <InteractiveKeyValueList
                        data={invoiceData["Products"]}
                        heading="Products"
                        selectedField={selectedField}
                        onFieldClick={setSelectedField}
                      />
                    )}
                    {visibleSections.taxDetails && (
                      <InteractiveKeyValueList
                        data={invoiceData["Tax Details"]}
                        heading="Tax Details"
                        selectedField={selectedField}
                        onFieldClick={setSelectedField}
                      />
                    )}
                    {visibleSections.totalAmount && (
                      <InteractiveKeyValueList
                        data={invoiceData["Total Amount"]}
                        heading="Total Amount"
                        selectedField={selectedField}
                        onFieldClick={setSelectedField}
                      />
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Invoice Data Fields Section */}
      <section className="bg-white py-16 lg:py-20">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Make faster decisions with insights from your documents
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Whether you're handling accounts payable or analyzing cash flow - 
              DocSynecX's intelligent algorithms help you extract the insights you need.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {/* Basic Information Card */}
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8 transform hover:scale-105 transition-all duration-500">
                <div className="flex items-center mb-6">
                  <FileText className="w-8 h-8 text-blue-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">Basic Information</h3>
                </div>
                
                <div className="space-y-4">
                  {[
                    { label: "Invoice Number", value: "X-7", delay: "0s" },
                    { label: "Invoice Date", value: "21/06/2018", delay: "0.2s" },
                    { label: "Due Date", value: "28/06/2018", delay: "0.4s" },
                    { label: "Place of Supply", value: "Outside India", delay: "0.6s" },
                    { label: "Currency", value: "USD", delay: "0.8s" }
                  ].map((field, index) => (
                    <div 
                      key={index}
                      className="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse"
                      style={{ animationDelay: field.delay, animationDuration: "2s" }}
                    >
                      <span className="text-gray-600">{field.label}:</span>
                      <span className="font-semibold text-gray-900">{field.value}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-6 flex items-center justify-center">
                  <div className="flex items-center text-blue-600">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    <span className="font-medium">Basic Details Verified</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Essential Information Card */}
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8 transform hover:scale-105 transition-all duration-500">
                <div className="flex items-center mb-6">
                  <FileText className="w-8 h-8 text-emerald-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">Essential Information</h3>
                </div>
                
                <div className="space-y-4">
                  {[
                    { label: "Company Name", value: "SC Alssac SRL", delay: "0.1s" },
                    { label: "Address", value: "Pune, JK [01], IN", delay: "0.3s" },
                    { label: "Phone", value: "+40744424757", delay: "0.5s" },
                    { label: "Email", value: "<EMAIL>", delay: "0.7s" },
                    { label: "GSTIN", value: "19AAXHS7580J1Z5", delay: "0.9s" }
                  ].map((field, index) => (
                    <div 
                      key={index}
                      className="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse"
                      style={{ animationDelay: field.delay, animationDuration: "2s" }}
                    >
                      <span className="text-gray-600">{field.label}:</span>
                      <span className="font-semibold text-gray-900">{field.value}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-6 flex items-center justify-center">
                  <div className="flex items-center text-emerald-600">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    <span className="font-medium">Essential Details Verified</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Transaction Analysis Card */}
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8 transform hover:scale-105 transition-all duration-500">
                <div className="flex items-center mb-6">
                  <FileText className="w-8 h-8 text-purple-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">Transaction Analysis</h3>
                </div>
                
                <div className="space-y-4">
                  {[
                    { label: "Taxable Amount", value: "₹90,000.00", delay: "0.2s" },
                    { label: "IGST Amount", value: "₹10,800.00", delay: "0.4s" },
                    { label: "Total Tax", value: "₹10,800.00", delay: "0.6s" },
                    { label: "Grand Total (INR)", value: "₹91,200.00", delay: "0.8s" },
                    { label: "Grand Total (USD)", value: "$1,337.00", delay: "1s" }
                  ].map((field, index) => (
                    <div 
                      key={index}
                      className="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse"
                      style={{ animationDelay: field.delay, animationDuration: "2s" }}
                    >
                      <span className="text-gray-600">{field.label}:</span>
                      <span className="font-semibold text-gray-900">{field.value}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-6 flex items-center justify-center">
                  <div className="flex items-center text-purple-600">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    <span className="font-medium">Financial Analysis Complete</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Stats Summary */}
          <div className="max-w-7xl mx-auto mt-12">
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-8 py-6 rounded-xl border border-gray-200">
              <div className="grid grid-cols-4 gap-8">
                <div className="text-center p-4 bg-white rounded-lg shadow-sm border border-gray-100">
                  <div className="text-2xl font-bold text-gray-900">15+</div>
                  <div className="text-sm text-gray-600">Fields Per Card</div>
                </div>
                <div className="text-center p-4 bg-white rounded-lg shadow-sm border border-gray-100">
                  <div className="text-2xl font-bold text-green-600">97.7%</div>
                  <div className="text-sm text-gray-600">Avg Accuracy</div>
                </div>
                <div className="text-center p-4 bg-white rounded-lg shadow-sm border border-gray-100">
                  <div className="text-2xl font-bold text-blue-600">0.8s</div>
                  <div className="text-sm text-gray-600">Processing Time</div>
                </div>
                <div className="text-center p-4 bg-white rounded-lg shadow-sm border border-gray-100">
                  <div className="text-2xl font-bold text-purple-600">24/7</div>
                  <div className="text-sm text-gray-600">Availability</div>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-12">
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 px-8 py-4 text-lg"
              onClick={() => window.open('https://app.docsynecx.com/signin/', '_blank')}
            >
              Get Started
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="px-8 py-4 text-lg border-teal-600 text-teal-600 hover:bg-teal-50"
              onClick={() => navigate('/solutions#usecase')}
            >
              Check All Use Cases
            </Button>
          </div>
        </div>
      </section>





      {/* Simple CTA Section */}
      <section className="bg-gradient-to-r from-teal-600 to-green-600 py-16 lg:py-20">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <div className="max-w-3xl mx-auto text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Transform Your Invoice Processing?
            </h2>
            <p className="text-xl text-teal-100 mb-8">
              Start processing invoices with 97.0% accuracy in seconds, not hours
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg">
                Start Free Trial
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg" onClick={() => {
                const form = document.getElementById('demo-request-form');
                if (form) form.scrollIntoView({ behavior: 'smooth' });
              }}>
                Book a Demo
              </Button>
            </div>
          </div>
        </div>
      </section>

      <DemoRequestForm />
      <Footer />
    </div>
  );
};

export default InvoiceProcessing;
