import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useState, useEffect } from "react";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import { initEmailJS, sendContactEmail } from "@/lib/emailjs";
import { useToast } from "@/hooks/use-toast";

const productOptions = [
  { label: "Retail Solution", value: "RETAIL_SOLUTION" },
  { label: "Manufacturing Solution", value: "MANUFACTURING_SOLUTION" },
  { label: "OCR Solution", value: "OCR_SOLUTION" },
];

const Contact = () => {

    // Set page title when component mounts
    useEffect(() => {
      document.title = "Contact Us - DocSynecX Document Intelligence Platform";
    }, []);
  // Submit Your Query form state
  const [queryForm, setQueryForm] = useState({
    fullName: "",
    email: "",
    phone: "",
    company: "",
    product: "",
    description: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Initialize EmailJS when component mounts
  useEffect(() => {
    initEmailJS();
  }, []);

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setQueryForm({ ...queryForm, [e.target.name]: e.target.value });
  };

  const handleQuerySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const result = await sendContactEmail(queryForm);

      if (result.success) {
        toast({
          title: "Message Sent Successfully! 🎉",
          description: "Thank you for contacting us. Our team will get back to you within 24 hours.",
        });
        setQueryForm({
          fullName: "",
          email: "",
          phone: "",
          company: "",
          product: "",
          description: ""
        });
      } else {
        toast({
          title: "Failed to Send Message",
          description: "There was a problem sending your message. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Network Error",
        description: "Could not send your message. Please check your connection and try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100 flex flex-col">
      <Navbar />
      <main className="flex-1 container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 text-center">Contact Us</h1>
          <p className="text-lg text-gray-600 mb-10 text-center">
            We're here to help. Whether you have questions about our AI solutions, need a demo, or want to discuss your business needs, our team is ready to assist you.
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form Section */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <Tabs defaultValue="general_inquiry" className="w-full">
                <TabsList className="w-full">
                  General Inquiry
                </TabsList>
                <TabsContent value="general_inquiry">
                  <form onSubmit={handleQuerySubmit} className="space-y-6 mt-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-gray-700 mb-1" htmlFor="fullName">Full Name<span className="text-red-500">*</span></label>
                        <input type="text" id="fullName" name="fullName" value={queryForm.fullName} onChange={handleQueryChange} required className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-teal-500 transition" />
                      </div>
                      <div>
                        <label className="block text-gray-700 mb-1" htmlFor="email">Email Address<span className="text-red-500">*</span></label>
                        <input type="email" id="email" name="email" value={queryForm.email} onChange={handleQueryChange} required className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-teal-500 transition" />
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-gray-700 mb-1" htmlFor="phone">Phone Number<span className="text-red-500">*</span></label>
                        <input type="tel" id="phone" name="phone" value={queryForm.phone} onChange={handleQueryChange} required className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-teal-500 transition" />
                      </div>
                      <div>
                        <label className="block text-gray-700 mb-1" htmlFor="company">Company Name</label>
                        <input type="text" id="company" name="company" value={queryForm.company} onChange={handleQueryChange} className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-teal-500 transition" />
                      </div>
                    </div>
                    <div>
                      <label className="block text-gray-700 mb-1" htmlFor="description">Your Message<span className="text-red-500">*</span></label>
                      <textarea id="description" name="description" value={queryForm.description} onChange={handleQueryChange} required rows={5} className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-teal-500 transition"></textarea>
                    </div>
                    <div>
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full bg-teal-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-transform transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? "Sending..." : "Send Message"}
                      </button>
                    </div>
                  </form>
                </TabsContent>
              </Tabs>
            </div>

            {/* Contact Info Section */}
            <div className="bg-teal-600 rounded-2xl shadow-lg p-8 text-white flex flex-col justify-between">
              <div>
                <h2 className="text-3xl font-bold mb-6">Contact Information</h2>
                <p className="text-teal-100 mb-8">
                  Have any questions or want to learn more? Reach out to us through any of the channels below.
                </p>
                <ul className="space-y-6">
                  <li className="flex items-start">
                    <svg className="w-6 h-6 mr-4 mt-1 text-teal-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>
                    <div>
                      <h4 className="font-bold text-lg">Send us a mail</h4>
                      <div className="space-y-1">
                        <div>
                          <span className="text-teal-200 text-sm">For general</span>
                          <br />
                          <a href="mailto:<EMAIL>" className="text-white hover:text-teal-200 transition"><EMAIL></a>
                        </div>
                        <div>
                          <span className="text-teal-200 text-sm">For Sales</span>
                          <br />
                          <a href="mailto:<EMAIL>" className="text-white hover:text-teal-200 transition"><EMAIL></a>
                        </div>
                      </div>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-6 h-6 mr-4 mt-1 text-teal-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg>
                    <div>
                      <h4 className="font-bold text-lg">Call us</h4>
                      <p className="text-teal-200 text-sm mb-1">Mon-Fri from 9am to 6pm.</p>
                      <a href="tel:04222212342" className="text-white hover:text-teal-200 transition">0422 2212342</a>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-6 h-6 mr-4 mt-1 text-teal-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>
                    <div>
                      <h4 className="font-bold text-lg">For Whatsapp</h4>
                      <a href="https://wa.me/919344776623" className="text-white hover:text-teal-200 transition">+91 93447 76623</a>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-6 h-6 mr-4 mt-1 text-teal-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                    <div>
                      <h4 className="font-bold text-lg">Meet us</h4>
                      <p className="text-teal-200 text-sm mb-1">Visit our Office</p>
                      <p className="text-white">S.F.NO- 497, Thadagam Main Road, Kanuvai, Coimbatore- 641108<br />Tamil Nadu, India</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Contact; 