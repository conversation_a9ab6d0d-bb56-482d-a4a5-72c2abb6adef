import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import PlatformHero from "@/components/platform/PlatformHero";
import PlatformFeatures from "@/components/platform/PlatformFeatures";
import PlatformWorkflow from "@/components/platform/PlatformWorkflow";
import PlatformCapabilities from "@/components/platform/PlatformCapabilities";
import PlatformCTA from "@/components/platform/PlatformCTA";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import Footer from "@/components/Footer";

const Platform = () => {

  // Set page title when component mounts
  useEffect(() => {
    document.title = "AI-Powered Document Intelligence Platform - DocSynecX";
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100">
      <Navbar />
      <PlatformHero />
      <PlatformFeatures />
      <PlatformWorkflow />
      <PlatformCapabilities />
      <PlatformCTA />
      <DemoRequestForm />
      <Footer />
    </div>
  );
};

export default Platform;
