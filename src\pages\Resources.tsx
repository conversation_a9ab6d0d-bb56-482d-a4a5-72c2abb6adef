import { FileText, BookOpen, Video, Download, ArrowRight, ExternalLink, Code, Database, Shield, Zap } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Navbar from "@/components/Navbar";
import ResourcesHero from "@/components/resources/ResourcesHero";
import ResourcesApiDocs from "@/components/resources/ResourcesApiDocs";
import ResourcesCTA from "@/components/resources/ResourcesCTA";
import ResourcesDemoForm from "@/components/resources/ResourcesDemoForm";
import Footer from "@/components/Footer";
import { useEffect } from "react";
const Resources = () => {

  useEffect(() => {
  document.title = "Resources - DocSynecX Document Intelligence Platform";
}, []);
  const documentation = [
    {
      title: "OCR API Documentation",
      description: "Complete API reference for integrating OCR capabilities into your applications",
      icon: Code,
      link: "/contact",
      category: "API"
    },
    {
      title: "SDK Libraries",
      description: "Client libraries for Python, JavaScript, Java, and other popular languages",
      icon: Database,
      link: "/contact",
      category: "SDK"
    },
    {
      title: "Integration Guides",
      description: "Step-by-step guides for integrating with popular platforms and tools",
      icon: Zap,
      link: "/contact",
      category: "Integration"
    },
    {
      title: "Security & Compliance",
      description: "Security best practices and compliance documentation for enterprise use",
      icon: Shield,
      link: "/contact",
      category: "Security"
    }
  ];

  const tutorials = [
    {
      title: "Getting Started with OCR API",
      description: "Learn how to make your first OCR API call and process documents",
      duration: "15 min",
      level: "Beginner",
      type: "Video"
    },
    {
      title: "Building an Invoice Processing App",
      description: "Complete tutorial on building an automated invoice processing application",
      duration: "45 min",
      level: "Intermediate",
      type: "Video"
    },
    {
      title: "Advanced OCR Configuration",
      description: "Deep dive into advanced OCR settings and optimization techniques",
      duration: "30 min",
      level: "Advanced",
      type: "Article"
    },
    {
      title: "Error Handling Best Practices",
      description: "Learn how to handle common OCR errors and edge cases effectively",
      duration: "20 min",
      level: "Intermediate",
      type: "Article"
    }
  ];

  const downloads = [
    {
      title: "OCR SDK for Python",
      description: "Official Python SDK with examples and documentation",
      downloads: "15,234",
      version: "v2.1.0",
      size: "2.3 MB"
    },
    {
      title: "OCR SDK for JavaScript",
      description: "Node.js and browser SDK for JavaScript applications",
      downloads: "12,891",
      version: "v1.8.2",
      size: "1.8 MB"
    },
    {
      title: "OCR SDK for Java",
      description: "Java SDK with Spring Boot integration examples",
      downloads: "8,456",
      version: "v2.0.1",
      size: "3.1 MB"
    },
    {
      title: "OCR SDK for .NET",
      description: "C# SDK with ASP.NET Core integration support",
      downloads: "6,234",
      version: "v1.9.3",
      size: "2.7 MB"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100">
      <Navbar />
      <ResourcesHero />
      <ResourcesApiDocs />
      <ResourcesDemoForm />
      <ResourcesCTA />
      <Footer />
    </div>
  );
};

export default Resources;
