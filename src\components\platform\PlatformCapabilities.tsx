import { <PERSON>, HardDrive, Setting<PERSON> } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const PlatformCapabilities = () => {
  return (
    <section className="container mx-auto px-6 py-20">
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold text-gray-900 mb-4">
          Seamless Integrations
        </h2>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Connect DocSynecX with your existing tools and workflows for streamlined document processing
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
        {/* Google Workspace */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center pb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-teal-600 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Cloud className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-xl">Google Workspace</CardTitle>
            <CardDescription>
              Drive, Sheets Integration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-center mb-4">
              Import documents from Google Drive and export processed data to Google Sheets for seamless workflow automation.
            </p>
            <div className="text-sm text-gray-500 space-y-1">
              <div>• Import from Google Drive</div>
              <div>• Export to Google Sheets</div>
              <div>• Real-time synchronization</div>
            </div>
          </CardContent>
        </Card>

        {/* OneDrive */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center pb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-teal-600 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <HardDrive className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-xl">OneDrive</CardTitle>
            <CardDescription>
              Microsoft Cloud Storage
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-center mb-4">
              Import documents directly from OneDrive for automated processing and intelligent data extraction.
            </p>
            <div className="text-sm text-gray-500 space-y-1">
              <div>• Direct OneDrive import</div>
              <div>• Automated file processing</div>
            </div>
          </CardContent>
        </Card>

        {/* Custom Integration */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center pb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-teal-600 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Settings className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-xl">Custom Integration</CardTitle>
            <CardDescription>
              Enterprise Systems
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-center mb-4">
              Integrate with specific Salesforce, ERP, SAP, CRM, or custom systems tailored to your business needs.
            </p>
            <div className="text-sm text-gray-500 space-y-1">
              <div>• Salesforce integration</div>
              <div>• ERP & SAP connectivity</div>
              <div>• Custom API development</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default PlatformCapabilities;
