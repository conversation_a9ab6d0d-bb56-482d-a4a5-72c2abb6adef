import { Check, X, FileText, Zap, Cog, BarChart3, Shield, Sparkles, Search, Upload, Eye, Database, Download, Settings, Users, Lock, Star } from "lucide-react";
import { Button } from "../ui/button";

const FeatureComparison = ({ billingCycle }: { billingCycle: string }) => {
  const plans = [
    { 
      name: "Free", 
      monthlyPrice: "$0",
      yearlyPrice: "$0",
      period: "/month",
      color: "border-gray-300",
      bgColor: "bg-gray-50",
      popular: false
    },
    { 
      name: "Explore", 
      monthlyPrice: "$85",
      yearlyPrice: "$77",
      period: "/month",
      color: "border-blue-500",
      bgColor: "bg-blue-50",
      popular: false
    },
    { 
      name: "Grow", 
      monthlyPrice: "$225",
      yearlyPrice: "$205",
      period: "/month",
      color: "border-green-500",
      bgColor: "bg-green-50",
      popular: true
    },
    { 
      name: "Premium", 
      monthlyPrice: "$450",
      yearlyPrice: "$405",
      period: "/month",
      color: "border-purple-500",
      bgColor: "bg-purple-50",
      popular: false
    },
    { 
      name: "Enterprise", 
      monthlyPrice: "Custom",
      yearlyPrice: "Custom",
      period: "",
      color: "border-orange-500",
      bgColor: "bg-orange-50",
      popular: false
    }
  ];

  const featureCategories = [
    {
      category: "Basic Processing",
      icon: Upload,
      features: [
        { name: "Auto-Image Quality Check", free: "Basic", explore: "Standard", grow: "Advanced", premium: "Advanced+", enterprise: "Custom" },
        { name: "Auto-orientation", free: "—", explore: "✓", grow: "✓", premium: "✓", enterprise: "✓" },
        { name: "Auto Document Splitting", free: "—", explore: "Up to 10 pages", grow: "Up to 25 pages", premium: "Unlimited", enterprise: "Unlimited" },
        { name: "File Type Support (PDF, PNG, JPEG, TIFF, BMP)", free: "PDF, JPEG only", explore: "All formats", grow: "All formats", premium: "All formats", enterprise: "All formats" },
        { name: "Batch Document Upload", free: "✓", explore: "✓", grow: "✓", premium: "✓", enterprise: "✓" }
      ]
    },
    {
      category: "OCR & AI Extraction",
      icon: Eye,
      features: [
        { name: "OCR Engine", free: "Basic", explore: "Standard", grow: "Advanced", premium: "Advanced+", enterprise: "Custom" },
        { name: "Extract Fields (Key-Value-Pair)", free: "✓", explore: "✓", grow: "✓", premium: "✓", enterprise: "✓" },
        { name: "Complex Table Parsing", free: "Basic tables", explore: "Basic tables", grow: "Advanced tables", premium: "Advanced tables", enterprise: "Custom tables" },
        { name: "Document Reviewer", free: "✓", explore: "✓", grow: "✓", premium: "✓", enterprise: "✓" },
        { name: "AI Document Categorization", free: "—", explore: "—", grow: "✓", premium: "✓", enterprise: "✓" },
        { name: "Custom Document Models", free: "—", explore: "—", grow: "—", premium: "Up to 5 models", enterprise: "Unlimited" }
      ]
    },
    {
      category: "Export & Integration",
      icon: Download,
      features: [
        { name: "Export Format (CSV, JSON)", free: "All formats", explore: "All formats", grow: "All formats", premium: "All formats", enterprise: "Custom formats" },
        { name: "API Access", free: "—", explore: "—", grow: "✓", premium: "✓", enterprise: "✓" },
        { name: "Prebuilt Integrations", free: "—", explore: "—", grow: "—", premium: "✓", enterprise: "Custom" },
      ]
    },
    {
      category: "Data Validation",
      icon: Database,
      features: [
        { name: "Data Formatting", free: "Basic", explore: "Standard", grow: "Advanced", premium: "Custom", enterprise: "Custom" },
        { name: "Data Lookups", free: "Manual", explore: "Basic", grow: "Advanced", premium: "Advanced+", enterprise: "Custom" },
        { name: "Post-Processing", free: "Limited", explore: "Basic", grow: "Standard", premium: "Advanced", enterprise: "Custom" }
      ]
    },
    {
      category: "Analytics",
      icon: BarChart3,
      features: [
        { name: "Dashboard & Metrics", free: "Basic", explore: "Standard", grow: "Advanced", premium: "Advanced", enterprise: "Custom" },
        { name: "Insights", free: "Limited", explore: "Basic", grow: "Standard", premium: "Advanced", enterprise: "Custom" }
      ]
    },
    {
      category: "Support",
      icon: Users,
      features: [
        { name: "Email Support", free: "✓", explore: "✓", grow: "✓", premium: "24/7", enterprise: "24/7" },
        { name: "Phone Support", free: "—", explore: "Business hrs", grow: "Business hrs", premium: "24/7", enterprise: "24/7" },
        { name: "Dedicated Manager", free: "—", explore: "—", grow: "—", premium: "—", enterprise: "✓" },
        { name: "SLA Guarantee", free: "—", explore: "—", grow: "—", premium: "✓", enterprise: "✓" },
        { name: "Onboarding & Training", free: "—", explore: "—", grow: "—", premium: "✓", enterprise: "✓" },
      ]
    },
    {
      category: "Security & Compliance",
      icon: Shield,
      features: [
        { name: "Data Encryption", free: "✓", explore: "✓", grow: "✓", premium: "✓", enterprise: "✓" },
        { name: "Role-Based Access", free: "—", explore: "—", grow: "—", premium: "✓", enterprise: "✓" },
        { name: "Audit Trails", free: "Basic", explore: "Standard", grow: "Advanced", premium: "Real-time", enterprise: "Custom" },
        { name: "Multi-Factor Auth", free: "—", explore: "—", grow: "—", premium: "✓", enterprise: "✓" },
        { name: "Security Policies", free: "✓", explore: "✓", grow: "✓", premium: "✓", enterprise: "✓" },
       
      ]
    },
    {
      category: "AI-Powered Enhancements",
      icon: Sparkles,
      features: [
        { name: "LLM-based Extraction", free: "Basic", explore: "Standard", grow: "Advanced", premium: "Advanced+", enterprise: "Custom" },
        { name: "Pre-trained Templates", free: "2 templates", explore: "5 templates", grow: "15 templates", premium: "50+", enterprise: "Custom" },
        { name: "AI Confidence Scoring", free: "✓", explore: "✓", grow: "✓", premium: "✓", enterprise: "✓" },
      ]
    }
  ];

  const FeatureCell = ({ value }: { value: any }) => {
    if (value === "✓") {
      return (
        <div className="flex justify-center">
          <span className="bg-white text-gray-900 px-2 md:px-3 py-1 md:py-1.5 rounded-full text-[10px] md:text-xs font-semibold border border-gray-300 flex items-center gap-1 md:gap-1.5">
            <Check className="w-2.5 h-2.5 md:w-3 md:h-3 text-green-600 flex-shrink-0" />
          </span>
        </div>
      );
    }
    if (value === "—") {
      return (
        <div className="flex justify-center">
          <span className="bg-gray-100 text-gray-500 px-2 md:px-3 py-1 md:py-1.5 rounded-full text-[10px] md:text-xs font-medium border border-gray-200 flex items-center gap-1 md:gap-1.5">
            —
          </span>
        </div>
      );
    }
    return (
      <div className="flex justify-center">
        <span className="bg-white text-gray-900 px-2 md:px-3 py-1 md:py-1.5 rounded-full text-[10px] md:text-xs font-semibold border border-gray-300 text-center leading-tight">
          {value}
        </span>
      </div>
    );
  };

  return (
    <div className="py-8 md:py-12 bg-white">
      <div className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8 md:mb-10">
          <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 text-sm font-medium rounded-full mb-4 md:mb-6">
            <Star className="w-4 h-4 mr-2" />
            Feature Comparison
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2 md:mb-4">
            Choose the Right Plan for Your Business
          </h2>
          <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto">
            Compare all features across our plans and find the perfect solution for your document processing needs
          </p>
        </div>

        {/* Responsive Table Wrapper */}
        <div className="w-full overflow-x-auto rounded-2xl shadow-xl bg-white border border-gray-200">
          <div className="min-w-[900px] md:min-w-[700px]">
            {/* Plan Headers */}
            <div className="bg-white border-b border-gray-200">
              <div className="grid grid-cols-6 gap-0">
                <div className="p-4 md:p-6 font-bold text-gray-900 border-r border-gray-200 bg-gray-50 min-w-[140px] md:min-w-[120px]">
                  <div className="flex items-center">
                    <FileText className="w-4 h-4 md:w-5 md:h-5 mr-2" />
                    <span className="text-sm md:text-base">Features</span>
                  </div>
                </div>
                {plans.map((plan, index) => (
                  <div
                    key={plan.name}
                    className={`py-3 md:py-4 px-2 md:px-3 text-center relative min-w-[140px] md:min-w-[140px] ${plan.popular ? 'bg-green-50' : 'bg-white'} ${index < plans.length - 1 ? 'border-r border-gray-200' : ''} hover:bg-gray-50 transition-all mt-3`}
                  >
                    {plan.popular && (
                      <div className="absolute -top-3 left-0 right-0 flex justify-center z-20">
                        <div className="bg-gradient-to-r from-green-600 to-emerald-700 text-white px-2 md:px-3 py-1 rounded-full text-[10px] md:text-[11px] font-semibold shadow-lg border border-green-400">
                          BEST VALUE
                        </div>
                      </div>
                    )}
                    <div className="flex flex-col items-center gap-1">
                      <div className="font-semibold text-gray-900 text-sm md:text-base">{plan.name}</div>
                      <div className="font-bold text-lg md:text-2xl text-gray-900">
                        {billingCycle === "monthly" ? plan.monthlyPrice : plan.yearlyPrice}
                        <span className="text-xs font-normal text-gray-500 ml-0.5">{plan.period}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Feature Categories */}
            <div>
              {featureCategories.map((category, categoryIndex) => (
                <div key={category.category} className={`${categoryIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                  {/* Category Header */}
                  <div className="px-3 md:px-4 py-2 md:py-3 bg-gray-100 border-b border-gray-200">
                    <div className="flex items-center">
                      <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-2 md:mr-3 shadow-md">
                        <category.icon className="w-3 h-3 md:w-4 md:h-4 text-white" />
                      </div>
                      <h3 className="text-sm md:text-base font-bold text-gray-900">{category.category}</h3>
                    </div>
                  </div>

                  {/* Feature Rows */}
                  <div>
                    {category.features.map((feature, featureIndex) => (
                      <div key={feature.name} className="grid grid-cols-6 gap-0 hover:bg-gray-50 transition-colors border-b border-gray-200 last:border-b-0">
                        <div className="p-2 md:p-3 font-medium text-gray-900 border-r border-gray-200 flex items-center bg-gray-50 min-w-[140px] md:min-w-[120px]">
                          <div className="flex items-center gap-1 md:gap-2">
                            <div className="w-4 h-4 md:w-5 md:h-5 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                              <FileText className="w-2 h-2 md:w-2.5 md:h-2.5 text-gray-700" />
                            </div>
                            <span className="text-xs md:text-sm leading-tight">{feature.name}</span>
                          </div>
                        </div>
                        {['free', 'explore', 'grow', 'premium', 'enterprise'].map((planKey, planIndex) => (
                          <div
                            key={planKey}
                            className={`p-2 md:p-3 flex items-center justify-center min-w-[140px] md:min-w-[140px] ${planIndex < 4 ? 'border-r border-gray-200' : ''} ${plans[planIndex].popular ? 'bg-green-50' : 'bg-white'}`}
                          >
                            <FeatureCell value={feature[planKey as keyof typeof feature]} />
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="bg-white border-t border-gray-200">
              <div className="grid grid-cols-6 gap-0">
                <div className="p-4 md:p-6 border-r border-gray-200"></div>
                {plans.map((plan, index) => (
                  <div key={plan.name} className={`p-3 md:p-6 text-center ${index < plans.length - 1 ? 'border-r border-gray-200' : ''}`}>
                    <Button 
                      className={`w-full font-semibold text-xs md:text-sm lg:text-base px-2 md:px-4 py-3 md:py-3 min-h-[44px] ${
                        plan.popular 
                          ? 'bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 text-white shadow-lg' 
                          : 'bg-white border-2 border-gray-300 text-gray-900 hover:bg-gray-50 hover:border-gray-400'
                      } transition-all duration-200 flex items-center justify-center gap-1 md:gap-2`}
                      onClick={() => window.open('https://app.docsynecx.com/signin/', '_blank')}
                    >
                      {plan.name === 'Free' && <Sparkles className="w-3 h-3 md:w-4 md:h-4 flex-shrink-0" />}
                      {plan.name === 'Explore' && <Search className="w-3 h-3 md:w-4 md:h-4 flex-shrink-0" />}
                      {plan.name === 'Grow' && <Zap className="w-3 h-3 md:w-4 md:h-4 flex-shrink-0" />}
                      {plan.name === 'Premium' && <Star className="w-3 h-3 md:w-4 md:h-4 flex-shrink-0" />}
                      {plan.name === 'Enterprise' && <Settings className="w-3 h-3 md:w-4 md:h-4 flex-shrink-0" />}
                      <span className="leading-tight text-center">
                        {plan.name === 'Free'
                          ? 'Get Started'
                          : plan.name === 'Enterprise'
                          ? 'Contact Sales'
                          : 'Choose Plan'}
                      </span>
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-green-600 via-emerald-600 to-green-700 rounded-2xl p-10 text-white shadow-2xl">
            <div className="flex items-center justify-center mb-4">
              <Sparkles className="w-8 h-8 mr-3" />
              <h3 className="text-3xl font-bold">Ready to Get Started?</h3>
            </div>
            <p className="text-green-100 mb-8 text-lg max-w-2xl mx-auto">
              Join thousands of businesses transforming their document processing with AI-powered automation
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                className="bg-white text-green-600 hover:bg-gray-100 font-bold px-8 py-3 text-lg rounded-xl shadow-lg"
                onClick={() => window.open('https://app.docsynecx.com/signin/', '_blank')}
              >
                Start Free Trial
              </Button>
              <Button 
                variant="outline" 
                className="border-2 border-white text-white hover:bg-white hover:text-green-600 font-bold px-8 py-3 text-lg rounded-xl bg-transparent"
                onClick={() => {
                  const form = document.getElementById('demo-request-form');
                  if (form) form.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Schedule Demo
              </Button>
            </div>
            <p className="text-green-200 text-sm mt-6">✓ No credit card required ✓ 14-day free trial ✓ Cancel anytime</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeatureComparison;
