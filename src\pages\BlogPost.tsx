import { <PERSON>L<PERSON>t, Clock, User, Tag, Share2, Heart, MessageCircle, Bookmark, Calendar, TrendingUp, Facebook, Twitter } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link, useParams, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import Footer from "@/components/Footer";
import { blogPosts } from "@/data/blogData";
import { formatDate } from "@/lib/utils";

const BlogPost = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const blogPost = blogPosts.find(post => post.slug === slug || post.id === slug);

  // Set dynamic page title based on blog post
  useEffect(() => {
    if (blogPost) {
      document.title = blogPost.title + " - DocSynecX";
    } else {
      document.title = "Blog Post Not Found - DocSynecX";
    }
  }, [blogPost, slug]);

  if (!blogPost) {
    return (
      <div className="container mx-auto px-6 py-20 text-center">
        <h1 className="text-3xl font-bold mb-4">Blog post not found</h1>
        <button onClick={() => navigate('/blog')} className="text-teal-600 underline">Back to Blog</button>
      </div>
    );
  }

  // Get related posts (exclude current post, sort by date, and limit to 3)
  const relatedPosts = blogPosts
    .filter(post => post.slug !== slug)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 3);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100">
      {/* Navigation */}
      <Navbar />

      {/* Back Button */}
      <div className="container mx-auto px-4 sm:px-6 py-6">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/blog')}
          className="flex items-center text-gray-600 hover:text-teal-600"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Blog
        </Button>
      </div>

      {/* Article Content */}
      <article className="container mx-auto px-4 sm:px-6 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Article Header */}
          <header className="mb-12">
            <div className="flex items-center justify-between mb-4">
              <span className="px-3 py-1 bg-teal-100 text-teal-700 rounded-full text-sm font-medium">
                {blogPost.category}
              </span>
              <div className="flex items-center text-gray-500 text-sm">
                <Clock className="w-4 h-4 mr-1" />
                {blogPost.readTime}
              </div>
            </div>
            
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {blogPost.title}
            </h1>
            
            <p className="text-lg sm:text-xl text-gray-600 mb-8 leading-relaxed">
              {blogPost.excerpt}
            </p>

            {/* Author and Meta Info */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mr-4 text-lg">
                  {blogPost.authorAvatar || blogPost.author[0]}
                </div>
                <div>
                  <div className="text-lg font-medium text-gray-900">{blogPost.author}</div>
                  {blogPost.authorRole && <div className="text-sm text-gray-500">{blogPost.authorRole}</div>}
                </div>
              </div>
              <div className="flex items-center text-gray-500 text-sm">
                <Calendar className="w-4 h-4 mr-1" />
                {formatDate(blogPost.date)}
              </div>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-8">
              {blogPost.tags.map((tag, index) => (
                <span key={index} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                  <Tag className="w-3 h-3 inline mr-1" />
                  {tag}
                </span>
              ))}
            </div>

            {/* Engagement Metrics */}
            <div className="flex items-center justify-between py-4 border-t border-b border-gray-200">
              <div className="flex items-center space-x-4 sm:space-x-6 text-sm text-gray-500">
                <span className="flex items-center">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  {blogPost.views} views
                </span>
                <span className="flex items-center">
                  <Heart className="w-4 h-4 mr-1" />
                  {blogPost.likes} likes
                </span>
                <span className="flex items-center">
                  <MessageCircle className="w-4 h-4 mr-1" />
                  {blogPost.comments} comments
                </span>
                <span className="flex items-center">
                  <Bookmark className="w-4 h-4 mr-1" />
                  {blogPost.bookmarks} bookmarks
                </span>
              </div>
              
              {/* Social Sharing */}
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" className="text-gray-500 hover:text-blue-600">
                  <Facebook className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-500 hover:text-blue-400">
                  <Twitter className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-500 hover:text-teal-600">
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* {["1", "2", "3"].includes(id) && (
              <p className="text-lg text-gray-700 mb-8">
                DocSynecX helps businesses reduce costs, improve efficiency, achieve high accuracy, and scale document processing effortlessly. Automate manual data entry, process documents faster, and handle thousands of files with confidence—all while maintaining industry-leading accuracy rates.
              </p>
            )} */}
          </header>

          {/* Article Body */}  
          <div className="mb-12">
            {blogPost.content}
          </div>

          {/* Article Footer */}
          <footer className="border-t border-gray-200 pt-8 mb-12">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button variant="ghost" className="text-gray-500 hover:text-red-500">
                  <Heart className="w-4 h-4 mr-2" />
                  Like Article
                </Button>
                <Button variant="ghost" className="text-gray-500 hover:text-teal-600">
                  <Bookmark className="w-4 h-4 mr-2" />
                  Bookmark
                </Button>
                <Button variant="ghost" className="text-gray-500 hover:text-teal-600">
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </footer>
        </div>
      </article>

      {/* Related Articles */}
      <section className="bg-gray-50 py-12 sm:py-16 lg:py-20">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-8">Related Articles</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
              {relatedPosts.map((post) => (
                <Card key={post.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="px-2 py-1 bg-teal-100 text-teal-700 rounded-full text-xs font-medium">
                        {post.category}
                      </span>
                      <div className="flex items-center text-gray-500 text-xs">
                        <Clock className="w-3 h-3 mr-1" />
                        {post.readTime}
                      </div>
                    </div>
                    <CardTitle className="text-lg group-hover:text-teal-600 transition-colors">
                      <Link to={`/blog/${post.slug || post.id}`}>{post.title}</Link>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm leading-relaxed mb-4">
                      {post.excerpt}
                    </CardDescription>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{post.author}</span>
                      <span>{post.date}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Demo Request Section */}
      <DemoRequestForm />

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-teal-600 to-green-600 py-12 sm:py-16 lg:py-20">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <div className="max-w-3xl mx-auto text-white">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6">
              Ready to Transform Your Document Workflows?
            </h2>
            <p className="text-lg sm:text-xl text-teal-100 mb-6 sm:mb-8">
              Experience the power of AI-powered OCR and document processing with DocSynecX
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center">
              <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg w-full sm:w-auto" asChild>
                <a href="https://app.docsynecx.com/signin/" target="_blank" rel="noopener noreferrer">
                  Start Free Trial
                </a>
              </Button>
              <Button size="lg" className="bg-white text-teal-600 hover:bg-gray-50 px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg w-full sm:w-auto" onClick={() => navigate("/contact")}>Contact Sales Team</Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default BlogPost; 
