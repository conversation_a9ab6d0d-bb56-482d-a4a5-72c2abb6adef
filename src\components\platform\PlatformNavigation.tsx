import { FileText } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const PlatformNavigation = () => {
  return (
    <nav className="container mx-auto px-6 py-4">
      <div className="flex items-center justify-between">
        <Link to="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-teal-600 to-green-600 rounded-lg flex items-center justify-center">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl font-bold bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
            DocSynecX
          </span>
        </Link>
        <div className="hidden md:flex items-center space-x-8">
          <Link to="/platform" className="text-teal-600 font-medium">Platform</Link>
          <Link to="/solutions" className="text-gray-600 hover:text-gray-900 transition-colors">Solutions</Link>
          <Link to="/blog" className="text-gray-600 hover:text-gray-900 transition-colors">Blog</Link>
          <a href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">Pricing</a>
          <Button variant="outline" className="mr-2">Sign In</Button>
          <Button className="bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700">
            Get Started
          </Button>
        </div>
      </div>
    </nav>
  );
};

export default PlatformNavigation;
