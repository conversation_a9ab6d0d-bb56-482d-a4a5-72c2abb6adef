import { Arrow<PERSON><PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const PlatformHero = () => {
  return (
    <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20 text-center">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight px-4">
          The Complete{" "}
          <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
            AI Platform
          </span>{" "}
          for Documents
        </h1>
        <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed px-4">
          Built for enterprise scale with advanced AI capabilities, robust security, and seamless integrations. 
          Transform your document workflows with the most powerful document AI platform available.
        </p>
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4">
          <Button size="lg" className="bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg w-full sm:w-auto" asChild>
            <a href="https://app.docsynecx.com/signin/" target="_blank" rel="noopener noreferrer">
              Start Free Trial
              <ArrowRight className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
            </a>
          </Button>
          <Button size="lg" variant="outline" className="px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg w-full sm:w-auto" onClick={() => {
            const form = document.getElementById('demo-request-form');
            if (form) form.scrollIntoView({ behavior: 'smooth' });
          }}>
            Schedule Demo
          </Button>
        </div>
      </div>
    </section>
  );
};

export default PlatformHero;
