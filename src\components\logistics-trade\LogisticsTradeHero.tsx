import { Button } from "@/components/ui/button";
import { ArrowRight, Truck, FileText, BarChart3 } from "lucide-react";

const LogisticsTradeHero = () => {
  const handleViewDemo = () => {
    const demoForm = document.getElementById('demo-request-form');
    if (demoForm) {
      demoForm.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="py-20 px-6 bg-white">
      <div className="container mx-auto text-center">
        <div className="flex justify-center mb-6">
          <div className="flex items-center space-x-2 bg-gradient-to-r from-teal-100 to-green-100 text-teal-700 px-4 py-2 rounded-full text-sm">
            <Truck className="w-4 h-4" />
            <span>Logistics & Trade Document Automation</span>
          </div>
        </div>
        <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
         Streamline Supply Chain Workflows
          <br />
          <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
           with Logistics Document AI
          </span>
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          Automate document processing for logistics and trade operations. Extract
          critical data from bills of lading, proof of delivery, e-way bills, and
          invoices with AI-powered precision.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Button
            size="lg"
            className="text-lg px-8 bg-gradient-to-r from-teal-600 to-green-600 text-white hover:from-teal-700 hover:to-green-700"
            onClick={() => window.open('https://app.docsynecx.com/signin/', '_blank')}
          >
            Start Free Trial
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="text-lg px-8 border-teal-600 text-teal-700 hover:bg-teal-50 hover:text-teal-900"
            onClick={handleViewDemo}
          >
            Request Demo
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Truck className="w-6 h-6 text-teal-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Smart Logistics
            </h3>
            <p className="text-gray-600">
              Automate supply chain documentation
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <FileText className="w-6 h-6 text-green-700" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Document AI
            </h3>
            <p className="text-gray-600">Extract data with 97.0% accuracy</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-teal-100 to-green-50 rounded-lg flex items-center justify-center mx-auto mb-4">
              <BarChart3 className="w-6 h-6 text-teal-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Real-time Analytics
            </h3>
            <p className="text-gray-600">Track and optimize operations</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LogisticsTradeHero;