// S<PERSON> to create the table (run in pgAdmin):
// CREATE TABLE demo_requests (
//   id SERIAL PRIMARY KEY,
//   name TEXT NOT NULL,
//   email TEXT NOT NULL,
//   company TEXT NOT NULL,
//   message TEXT,
//   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
// );

const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
app.use(cors());
app.use(express.json());

// Configure your PostgreSQL connection
const pool = new Pool({
  user: 'postgres', // replace with your PostgreSQL user
  host: 'localhost',
  database: 'Demo_Details', // replace with your database name
  password: '1234', // replace with your password
  port: 5432, // default PostgreSQL port
});

app.post('/api/request-demo', async (req, res) => {
  const { name, email, company, message } = req.body;
  try {
    await pool.query(
      'INSERT INTO demo_requests (name, email, company, message) VALUES ($1, $2, $3, $4)',
      [name, email, company, message]
    );
    res.json({ success: true });
  } catch (err) {
    console.error(err);
    res.status(500).json({ success: false, error: 'Database error' });
  }
});

app.listen(4000, () => console.log('Server running on port 4000')); 