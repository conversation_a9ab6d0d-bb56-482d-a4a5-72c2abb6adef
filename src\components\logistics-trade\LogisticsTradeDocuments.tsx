import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, FileCheck2, FileSearch2 } from "lucide-react";

const LogisticsTradeDocuments = () => (
  <section className="py-16 bg-white">
    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
      <div className="text-center max-w-2xl mx-auto mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Supported Logistics & Trade Documents
        </h2>
        <p className="text-lg text-gray-600">
          Extract data from all major logistics, shipping, and trade documents with AI-powered OCR.
        </p>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
        <div className="bg-gradient-to-br from-teal-50 to-green-50 rounded-xl shadow p-6 flex flex-col items-center">
          <FileText className="w-10 h-10 text-teal-600 mb-4" />
          <h3 className="font-semibold text-lg text-gray-900 mb-2">Bills of Lading</h3>
          <p className="text-gray-600 text-center text-sm">Automate extraction from ocean, air, and road bills of lading.</p>
        </div>
        <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-xl shadow p-6 flex flex-col items-center">
          <FileCheck2 className="w-10 h-10 text-green-600 mb-4" />
          <h3 className="font-semibold text-lg text-gray-900 mb-2">Proof of Delivery</h3>
          <p className="text-gray-600 text-center text-sm">Digitize and validate delivery receipts and PODs.</p>
        </div>
        <div className="bg-gradient-to-br from-teal-50 to-green-50 rounded-xl shadow p-6 flex flex-col items-center">
          <FileSearch2 className="w-10 h-10 text-teal-600 mb-4" />
          <h3 className="font-semibold text-lg text-gray-900 mb-2">E-way Bills & Invoices</h3>
          <p className="text-gray-600 text-center text-sm">Extract data from e-way bills, invoices, and customs docs.</p>
        </div>
      </div>
    </div>
  </section>
);

export default LogisticsTradeDocuments;