import { useRef, useState } from "react";
import { useNavigate } from "react-router-dom"; // Add this import
import { ChevronRight, ChevronDown } from "lucide-react";
import { useEffect } from "react";  

// Sidebar structure with expandable sections
const toc = [
    {
        label: "Getting Started",
        children: [
            { id: "welcome", label: "Welcome" },
            { id: "about", label: "About Docsynecx" },
            { id: "what-youll-find", label: "What’s Inside" },
            { id: "getting-started", label: "Quick Start" },
        ],
    },
    {
        label: "Platform Features",
        children: [
            { id: "ai-model-hub", label: "AI Model Hub" },
            { id: "automation-workflows", label: "Automation Workflows" },
            { id: "features", label: "Features" },
        ],
    },
    {
        label: "Support & Resources",
        children: [
            { id: "faqs", label: "FAQs" },
            { id: "best-practices", label: "Best Practices" },
            { id: "need-help", label: "Need Help?" },
        ],
    },
    {
        label: "Guides",
        children: [
            { id: "work-smarter", label: "Work Smarter" },
            { id: "visual-guide", label: "Visual Guide" },
            { id: "step1", label: "Step 1: Dashboard" },
            { id: "step2", label: "Step 2: Document Type" },
            { id: "step3", label: "Step 3: Upload Document" },
            { id: "step4", label: "Step 4: AI Extraction" },
            { id: "step5", label: "Step 5: Review Output" },
            { id: "step6", label: "Step 6: Export Data" },
        ],
    },
];

const HEADER_HEIGHT = 72; // px, adjust if your header height changes

const Help = () => {
    useEffect(() => {
  document.title = "AI-driven Intelligent Document Processing (IDP) Platform - Docsynecx Help Center";
}, []);
    const navigate = useNavigate(); // Add this line
    const sectionRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
    const [openMenus, setOpenMenus] = useState<string[]>([toc[0].label, toc[3].label]);

    // Scroll with offset so headings are not hidden behind the header
    const handleTocClick = (id: string) => {
        const el = sectionRefs.current[id];
        if (el) {
            const y = el.getBoundingClientRect().top + window.scrollY - HEADER_HEIGHT - 16; // 16px extra space
            window.scrollTo({ top: y, behavior: "smooth" });
        }
    };

    const toggleMenu = (label: string) => {
        setOpenMenus((prev) =>
            prev.includes(label) ? prev.filter((l) => l !== label) : [...prev, label]
        );
    };

    return (
        <div className="min-h-screen bg-white flex flex-col font-sans">
            {/* Header */}
            <header className="w-full bg-white shadow-sm sticky top-0 z-30 border-b border-slate-100" style={{ height: HEADER_HEIGHT }}>
                <div className="max-w-7xl mx-auto flex items-center justify-between px-3 sm:px-6" style={{ height: HEADER_HEIGHT }}>
                    <div className="flex items-center gap-3">
                        <img src="/logo-icon.svg" alt="Docsynecx" className="h-10 w-10" />
                        <span className="text-lg sm:text-2xl font-bold sm:font-extrabold text-teal-700 tracking-tight">
                            Docsynecx Help Center
                        </span>
                    </div>
                    <button
                        onClick={() => navigate('/')}
                        className="text-teal-700 hover:text-teal-900 font-medium px-3 sm:px-4 py-2 rounded transition"
                    >
                        Go to App
                    </button>
                </div>
            </header>

            {/* Main Content */}
            <div className="flex flex-1 max-w-7xl mx-auto w-full">
                {/* Sidebar */}
                <aside
                    className="hidden md:flex flex-col w-72 bg-gradient-to-b from-teal-50 via-green-50 to-white border-r border-teal-100 pt-10 pb-6 px-5 sticky top-[72px] h-[calc(100vh-72px)] overflow-y-auto shadow-md rounded-r-2xl"
                    aria-label="Help Table of Contents"
                >
                    <nav>
                        <h2 className="text-lg font-bold text-teal-700 mb-6 pl-2 flex items-center gap-2">
                            <span className="inline-block w-2 h-2 bg-gradient-to-r from-teal-400 to-green-400 rounded-full"></span>
                            Docs
                        </h2>
                        <ul className="space-y-2">
                            {toc.map((section) => (
                                <li key={section.label} className="rounded-lg overflow-hidden">
                                    <button
                                        className={`flex items-center w-full px-3 py-2 text-left font-semibold transition
                                            ${openMenus.includes(section.label)
                                                ? "bg-gradient-to-r from-teal-100 to-green-50 text-teal-700"
                                                : "text-gray-700 hover:bg-teal-50 hover:text-teal-700"}
                                            rounded-lg group`}
                                        onClick={() => toggleMenu(section.label)}
                                    >
                                        <span className="flex-1">{section.label}</span>
                                        {openMenus.includes(section.label) ? (
                                            <ChevronDown className="w-4 h-4 ml-2 text-teal-500 transition-transform" />
                                        ) : (
                                            <ChevronRight className="w-4 h-4 ml-2 text-gray-400 group-hover:text-teal-500 transition-transform" />
                                        )}
                                    </button>
                                    <ul className={`pl-4 mt-1 space-y-1 transition-all ${openMenus.includes(section.label) ? "max-h-96" : "max-h-0 overflow-hidden"}`}>
                                        {section.children.map((item) => {
                                            const isActive = window.location.hash.replace('#', '') === item.id;
                                            return (
                                                <li key={item.id}>
                                                    <button
                                                        onClick={() => handleTocClick(item.id)}
                                                        className={`w-full text-left px-3 py-2 rounded-lg text-base font-medium focus:outline-none transition-colors
                                                            ${
                                                                isActive
                                                                    ? "bg-gradient-to-r from-teal-600 to-green-500 text-white shadow font-bold ring-2 ring-green-300"
                                                                    : openMenus.includes(section.label)
                                                                        ? "text-teal-700 hover:bg-teal-100"
                                                                        : "text-gray-600 hover:bg-teal-50 hover:text-teal-700"
                                                            }`}
                                                    >
                                                        {item.label}
                                                    </button>
                                                </li>
                                            );
                                        })}
                                    </ul>
                                </li>
                            ))}
                        </ul>
                    </nav>
                </aside>

                {/* Main Article */}
                <main className="flex-1 px-3 sm:px-6 py-8 sm:py-12 bg-white">
                    <article className="max-w-3xl mx-auto">
                        {/* Welcome */}
                        <section
                            ref={el => (sectionRefs.current["welcome"] = el)}
                            className="mb-14 scroll-mt-[88px]"
                        >
                            <h1 className="text-2xl sm:text-4xl font-bold sm:font-extrabold text-teal-700 mb-4 tracking-tight">
                                Welcome to the Docsynecx Knowledge Base
                            </h1>
                            <p className="text-base sm:text-lg text-gray-700 mb-2">
                                We’re thrilled to welcome you to the <b>Docsynecx</b> platform — your partner in intelligent document automation. Whether you’re here to streamline invoice processing, explore our AI capabilities, or integrate smart workflows into your operations, this knowledge base has been thoughtfully crafted to guide you every step of the way.
                            </p>
                            <p className="text-base sm:text-lg text-gray-700">
                                We know adopting a new platform can come with questions, so our goal is to make your journey as seamless and rewarding as possible. You’ll find step-by-step guides, deep product insights, practical tips, and answers to your most common questions — all in one place.
                            </p>
                        </section>

                        {/* About */}
                        <section ref={el => (sectionRefs.current["about"] = el)} className="mb-12">
                            <h2 className="text-2xl font-bold text-teal-700 mb-3">About Docsynecx</h2>
                            <ul className="list-disc pl-6 text-gray-700 space-y-2">
                                <li><b>Docsynecx</b> is an innovative, AI-driven Intelligent Document Processing (IDP) platform developed by <b>SynecX AI Labs</b>. It’s designed to help modern businesses eliminate manual data entry, reduce processing delays, and automate the handling of complex document types with remarkable ease.</li>
                                <li>Powered by <b>Generative AI, OCR, Computer Vision</b>, and <b>Large Language Models (LLMs)</b>, Docsynecx intelligently extracts and validates data from invoices, purchase orders, receipts, shipping labels, contracts, medical forms, and more — regardless of format, language, or layout.</li>
                                <li>Our platform is trusted across industries like <b>finance, logistics, retail, and healthcare</b>, enabling teams to work faster, reduce errors, stay compliant, and shift their focus from low-value tasks to strategic decision-making. Whether you're a startup managing your first 100 invoices or an enterprise processing thousands daily, Docsynecx scales effortlessly with your needs.</li>
                            </ul>
                        </section>

                        {/* What You’ll Find */}
                        <section ref={el => (sectionRefs.current["what-youll-find"] = el)} className="mb-12">
                            <h2 className="text-2xl font-bold text-teal-700 mb-3">What You’ll Find in This Knowledge Base</h2>
                            <p className="text-gray-700 mb-2">
                                This guide is your all-in-one resource to mastering Docsynecx. Whether you’re just getting started or already diving into advanced integrations, we’ve organized the content to suit users of all skill levels — from business analysts and ops managers to developers and automation architects.
                            </p>
                            <ul className="list-disc pl-6 text-gray-700">
                                <li>Getting Started</li>
                                <li>AI Model Hub</li>
                                <li>End-to-End Automation Workflows</li>
                                <li>Product Features Deep Dive</li>
                                <li>FAQs</li>
                                <li>Best Practices & Real-World Use Cases</li>
                            </ul>
                        </section>

                        {/* Getting Started */}
                        <section ref={el => (sectionRefs.current["getting-started"] = el)} className="mb-12">
                            <h2 className="text-xl font-semibold text-teal-700 mb-2">Getting Started</h2>
                            <div className="bg-teal-50 p-4 rounded border-l-4 border-teal-400">
                                <p className="text-gray-700">
                                    <span className="font-semibold">New to Docsynecx?</span> Learn how to set up your account, configure your environment, and run your first document through the platform in just a few minutes. We’ll also walk you through any system requirements and permissions to ensure a smooth start.
                                </p>
                            </div>
                        </section>

                        {/* AI Model Hub */}
                        <section ref={el => (sectionRefs.current["ai-model-hub"] = el)} className="mb-12">
                            <h2 className="text-xl font-semibold text-teal-700 mb-2">AI Model Hub</h2>
                            <p className="text-gray-700">
                                Explore our extensive library of pre-trained AI models for common document types like invoices, bills of lading, and tax forms. You’ll also learn how to adapt or fine-tune these models to meet your unique business needs, all with minimal technical effort.
                            </p>
                        </section>

                        {/* End-to-End Automation Workflows */}
                        <section ref={el => (sectionRefs.current["automation-workflows"] = el)} className="mb-12">
                            <h2 className="text-xl font-semibold text-teal-700 mb-2">End-to-End Automation Workflows</h2>
                            <p className="text-gray-700">
                                See how to build full, production-ready workflows — from document ingestion and data extraction, to validation, review, and API integration. Whether you're automating your accounts payable process or managing logistics documents, our guides show you how to design it right.
                            </p>
                        </section>

                        {/* Product Features */}
                        <section ref={el => (sectionRefs.current["features"] = el)} className="mb-12">
                            <h2 className="text-xl font-semibold text-teal-700 mb-2">Product Features Deep Dive</h2>
                            <p className="text-gray-700">
                                Take a closer look at the full feature set of Docsynecx. Learn how to configure auto-classification, implement confidence scoring, track field-level accuracy, perform real-time validations, and connect your workflows to systems like SAP, QuickBooks, or Tally — all with no-code and API options.
                            </p>
                        </section>


                        {/* FAQs */}
                        <section ref={el => (sectionRefs.current["faqs"] = el)} className="mb-12">
                            <h2 className="text-xl font-semibold text-teal-700 mb-2">FAQs</h2>
                            <p className="text-gray-700">
                                Got questions? We've got answers. From billing to technical troubleshooting, our FAQ section addresses the most common queries with clear and concise responses, so you can get back to work quickly.
                            </p>
                        </section>

                        {/* Best Practices */}
                        <section ref={el => (sectionRefs.current["best-practices"] = el)} className="mb-12">
                            <h2 className="text-xl font-semibold text-teal-700 mb-2">Best Practices & Real-World Use Cases</h2>
                            <p className="text-gray-700">
                                Get inspired by real businesses using Docsynecx to transform their operations. From finance teams cutting invoice times by 80% to logistics companies improving compliance — these practical stories and expert tips will help you get the most value from the platform.
                            </p>
                        </section>


                        {/* Need Help */}
                        <section ref={el => (sectionRefs.current["need-help"] = el)} className="mb-12">
                            <h2 className="text-xl font-semibold text-teal-700 mb-2">Need Help Along the Way?</h2>
                            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                                <p className="text-gray-700">
                                    We’ve put a lot of effort into making this documentation clear, complete, and actionable — but we also know that every business is unique. If you ever find yourself stuck, have a question that isn’t covered, or just want to talk to a real person, we’re here for you.
                                </p>
                                <p className="mt-2">
                                    <span className="text-gray-700">Reach out to our friendly support team at</span>{" "}
                                    <a href="mailto:<EMAIL>" className="text-teal-700 underline font-semibold"><EMAIL></a>,
                                    <span className="text-gray-700"> and we’ll get back to you as quickly as possible. Whether it’s a technical issue, feature question, or product feedback, we genuinely care about your success.</span>
                                </p>

                            </div>
                        </section>

                        {/* Work Smarter */}
                        <section ref={el => (sectionRefs.current["work-smarter"] = el)} className="mb-12">
                            <h2 className="text-xl font-semibold text-teal-700 mb-2">Work Smarter, Not Harder — with Docsynecx</h2>
                            <div className="bg-gradient-to-r from-teal-50 to-green-50 rounded p-4">
                                <p className="text-gray-700">
                                    At Docsynecx, we believe automation should be intelligent, intuitive, and empowering. With our platform, you're not just reducing manual effort — you're unlocking a faster, more strategic, and scalable way to work.
                                </p>
                                <p className="text-gray-700 mt-2">
                                    Let this knowledge base be your launchpad to automation success. We’re excited to see what you’ll build. <span className="font-semibold">Happy automating!</span>
                                </p>
                            </div>
                        </section>

                        {/* Visual Guide */}
                        <section ref={el => (sectionRefs.current["visual-guide"] = el)} className="mb-12">
                            <h2 className="text-2xl font-bold text-teal-700 mb-3">How to Use Docsynecx: A Visual Guide to Getting Started</h2>
                            <p className="text-gray-700 mb-4">
                                Welcome to <b>Docsynecx</b> — your intelligent automation partner for document processing. In just a few simple steps, you’ll learn how to upload a document, extract key data using our powerful AI, review the output, and export it directly into your ERP system. This guide walks you through the platform using real visuals from the Docsynecx interface, helping you get up and running quickly and confidently.
                            </p>
                            <img src="/HelpImages/1.png" alt="Dashboard" className="rounded shadow mb-4 border border-teal-100" />
                        </section>

                        {/* Steps */}
                        <section ref={el => (sectionRefs.current["step1"] = el)} className="mb-12">
                            <h3 className="text-lg font-bold text-teal-700 mb-2">Step 1: Landing on Your Dashboard</h3>
                            <p className="text-gray-700 mb-4">
                                Once you log into the Docsynecx app, you're welcomed by a clean and intuitive dashboard. This is your <b>central workspace</b> where all uploaded documents are listed. You’ll find essential document details such as file name, type, number of pages, processing status, and available actions. From here, you can filter between processed and pending documents or search through your existing files effortlessly.
                            </p>

                            <p className="text-gray-700 mb-4">
                                This view includes helpful metadata such as the document name, type (e.g., Tax Invoice), number of pages, creation and modification dates, status, and quick actions. From here, you can filter by document status <b>(e.g., Processed, Review)</b>, search for specific files, or sort based on any column.
                            </p>

                            <p className="text-gray-700 mb-4">
                                When you're ready to begin processing a new file, simply click on the green <span className="font-semibold">“+ Create Document”</span> button in the top right.
                            </p>
                            <img src="/HelpImages/2.png" alt="Dashboard" className="rounded shadow mb-4 border border-teal-100" />

                        </section>

                        <section ref={el => (sectionRefs.current["step2"] = el)} className="mb-12">
                            <h3 className="text-lg font-bold text-teal-700 mb-2">Step 2: Select a Document Type</h3>

                            <p className="text-gray-700 mb-4">
                                After clicking “Create Document,” a modal window appears prompting you to <b>select a document type</b>. This step is important because it tells the AI engine what to look for in terms of structure and field extraction.
                            </p>

                            <p className="text-gray-700 mb-4">
                                Docsynecx offers a wide variety of <b>pre-trained document types</b>, grouped by categories like Invoices, Receipts, and Shipping Labels. Each template is built using real-world data and fine-tuned to extract key information like invoice numbers, buyer details, totals, GST numbers, and more. Simply search or browse the list and select a relevant type — for instance, “Tax Invoice” or “Purchase Order.”
                            </p>

                            <p className="text-gray-700 mb-4">
                                If none of the pre-trained templates match your needs, you can always create a <b>custom document type</b> later and define fields manually.
                            </p>

                            <p className="text-gray-700 mb-4">
                                This setup is crucial because it enables <b>template-free AI extraction</b>, meaning you don’t have to create zones, rules, or formats for each layout — the system understands what to look for automatically.
                            </p>

                            <img src="/HelpImages/3.png" alt="Dashboard" className="rounded shadow mb-4 border border-teal-100" />
                        </section>


                        <section ref={el => (sectionRefs.current["step3"] = el)} className="mb-12">
                            <h3 className="text-lg font-bold text-teal-700 mb-2">Step 3: Upload Your Document</h3>

                            <p className="text-gray-700 mb-4">
                                Once your document type is selected, the next step is to upload your file. You’ll see a familiar drag-and-drop upload interface. Docsynecx supports a wide range of document formats including:
                            </p>

                            <ul className="list-disc pl-6 text-gray-700 mb-4">
                                <li><b>PDF</b> (up to 10 pages per file)</li>
                                <li><b>JPG / JPEG / PNG</b> images (up to 10MB)</li>
                            </ul>

                            <p className="text-gray-700 mb-4">
                                You can either click the upload box to browse your computer or simply drag your file into the highlighted area. After selecting the file, the system will show a preview of the uploaded document. Click <b>“Create”</b> to confirm.
                            </p>

                            <p className="text-gray-700 mb-4">
                                This action triggers the backend AI to begin processing your file. The system assigns it a status like “Processing” or “Review,” and it will appear in your document list shortly after.
                            </p>
                            <img src="/HelpImages/4.png" alt="Dashboard" className="rounded shadow mb-4 border border-teal-100" />

                        </section>

                        <section ref={el => (sectionRefs.current["step4"] = el)} className="mb-12">
                            <h3 className="text-lg font-bold text-teal-700 mb-2">Step 4: AI-Powered Data Extraction Begins</h3>

                            <p className="text-gray-700 mb-4">
                                As soon as your document is created, Docsynecx begins analyzing it using its proprietary AI engine. This includes:
                            </p>

                            <ul className="list-disc pl-6 text-gray-700 mb-4">
                                <li>Enhancing the document image (e.g., de-noising or deskewing)</li>
                                <li>Reading and interpreting layout structures</li>
                                <li>Automatically identifying and extracting values from relevant fields</li>
                            </ul>

                            <p className="text-gray-700 mb-4">
                                When you open a processed file, you’ll see a <b>split-screen interface</b>:
                            </p>

                            <p className="text-gray-700 mb-4">
                                📄 On the <b>left</b>, your original document is displayed in full.
                            </p>

                            <p className="text-gray-700 mb-4">
                                📊 On the <b>right</b>, the <b>“Structured Data”</b> panel shows all the values extracted by the AI — neatly categorized by sections like <b>Basic Information</b>, <b>Seller Details</b>, <b>Buyer Details</b>, and <b>Product Line Items</b>.
                            </p>

                            <p className="text-gray-700 mb-4">
                                You’ll immediately notice how the platform recognizes complex data like multi-line addresses, GSTIN numbers, invoice totals, and tax breakdowns — all without needing any manual configuration.
                            </p>
                            <img src="/HelpImages/5.png" alt="Dashboard" className="rounded shadow mb-4 border border-teal-100" />

                        </section>

                        <section ref={el => (sectionRefs.current["step5"] = el)} className="mb-12">
                            <h3 className="text-lg font-bold text-teal-700 mb-2">Step 5: Review the Output</h3>

                            <p className="text-gray-700 mb-4">
                                Once the document is processed, it’s time to review the results. The extracted fields are <b>editable in-line</b>, so you can make corrections or enhancements before saving. This review step ensures data accuracy before exporting to downstream systems.
                            </p>

                            <p className="text-gray-700 mb-4">
                                The system assigns confidence scores in the backend, and over time, it learns from manual corrections — meaning the more you use Docsynecx, the smarter it becomes.
                            </p>
                        </section>
                        <img src="/HelpImages/6.png" alt="Dashboard" className="rounded shadow mb-4 border border-teal-100" />
                        <img src="/HelpImages/7.png" alt="Dashboard" className="rounded shadow mb-4 border border-teal-100" />

                        <section ref={el => (sectionRefs.current["step6"] = el)} className="mb-12">
                            <h3 className="text-lg font-bold text-teal-700 mb-2">Step 6: Export the Structured Data</h3>

                            <p className="text-gray-700 mb-4">
                                Once you’ve validated the extracted information, you’re ready to export. Simply click on the <b>“Download”</b> button at the bottom of the structured data panel.
                            </p>

                            <p className="text-gray-700 mb-4">
                                You can choose your export format based on your needs:
                            </p>

                            <ul className="list-disc pl-6 text-gray-700 mb-4">
                                <li><b>CSV</b> for Excel, data processing, or business dashboards</li>
                                <li><b>JSON</b> for API integrations, databases, or developer workflows</li>
                            </ul>

                            <p className="text-gray-700 mb-4">
                                The exported file contains a clean structure of the data — ready to be imported into other applications like your accounting software, data warehouse, or reporting tools.
                            </p>
                        </section>

                        {/* Success & Support Section */}
                        <section className="mb-12 scroll-mt-[96px]">
                            <div className="bg-gradient-to-r from-green-50 to-teal-50 border-l-4 border-teal-400 p-4 rounded mb-6">
                                <h3 className="text-lg font-bold text-teal-700 mb-2">🎉 That’s It — You’re Ready!</h3>
                                <p className="text-gray-700 mb-2">
                                    <b>You’ve just gone through the entire workflow</b> — from document upload to data review — powered by AI, optimized for speed, and designed for ease of use.
                                </p>
                                <p className="text-gray-700">
                                    Whether you’re handling hundreds of invoices or onboarding new vendors, <b>Docsynecx empowers you to automate smarter</b>. By eliminating repetitive work and reducing manual touchpoints, your team can focus on <b>high-value activities and strategic growth</b>.
                                </p>
                            </div>
                            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                                <h3 className="text-lg font-bold text-teal-700 mb-2">💬 Need Help?</h3>
                                <p className="text-gray-700">
                                    If you have any questions or run into issues, our support team is always just a message away. Email us at <a href="mailto:<EMAIL>" className="font-bold underline"><EMAIL></a> and we’ll help you resolve anything <b>quickly and efficiently</b>.
                                </p>
                            </div>
                        </section>

                    </article>
                </main>
            </div>
        </div>
    );
};

export default Help;
