import DocumentProcessingHero from "@/components/document-processing/DocumentProcessingHero";
import DocumentProcessingSolutions from "@/components/document-processing/DocumentProcessingSolutions";
import DocumentProcessingProcess from "@/components/document-processing/DocumentProcessingProcess";
import DocumentProcessingCTA from "@/components/document-processing/DocumentProcessingCTA";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import { useEffect } from "react";

const DocumentProcessing = () => {
     useEffect(() => {
        document.title = "Document AI-Platform for Document Processing";
      }, []);
    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-green-100 flex flex-col">
            <Navbar />
            <main className="flex-1">
                <DocumentProcessingHero />
                <DocumentProcessingSolutions />
                <DocumentProcessingProcess />
                <DocumentProcessingCTA />
                {/* <DemoRequestForm /> */}
            </main>
            <Footer />
        </div >
    );
};

export default DocumentProcessing;