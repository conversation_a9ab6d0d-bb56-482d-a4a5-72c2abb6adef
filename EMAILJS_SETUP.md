# EmailJS Setup Instructions

This document provides step-by-step instructions to set up EmailJS for the contact form and demo request forms in the DocSynecX application.

## Prerequisites

1. Create an account at [EmailJS](https://www.emailjs.com/)
2. Verify your email address

## Step 1: Create EmailJS Service

1. Log in to your EmailJS dashboard
2. Go to **Email Services** section
3. Click **Add New Service**
4. Choose your email provider (Gmail, Outlook, etc.)
5. Follow the setup instructions for your provider
6. Note down the **Service ID** (e.g., `service_docsynecx`)

## Step 2: Create Email Templates

### Contact Form Template

1. Go to **Email Templates** section
2. Click **Create New Template**
3. Name it "Contact Form" 
4. Set the template content:

```html
Subject: New Contact Form Submission from {{from_name}}

Hello {{to_name}},

You have received a new contact form submission:

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}
Company: {{company}}
Product Interest: {{product}}

Message:
{{message}}

Best regards,
DocSynecX Contact System
```

4. Note down the **Template ID** (e.g., `template_contact`)

### Demo Request Template

1. Create another template named "Demo Request"
2. Set the template content:

```html
Subject: New Demo Request from {{from_name}}

Hello {{to_name}},

You have received a new demo request:

Name: {{from_name}}
Email: {{from_email}}
Company: {{company}}

Requirements:
{{message}}

Please contact them within 24 hours to schedule the demo.

Best regards,
DocSynecX Demo System
```

3. Note down the **Template ID** (e.g., `template_demo`)

## Step 3: Get Public Key

1. Go to **Account** section in EmailJS dashboard
2. Find your **Public Key** (User ID)
3. Note it down (e.g., `your_public_key_here`)

## Step 4: Update Configuration

Update the file `src/lib/emailjs.ts` with your actual values:

```typescript
export const EMAILJS_CONFIG = {
  SERVICE_ID: 'service_dbf6de6',        // Replace with your service ID
  TEMPLATE_ID_CONTACT: 'template_0bfyahq', // Replace with contact template ID
  TEMPLATE_ID_DEMO: 'template_p505oeb',       // Replace with demo template ID
  PUBLIC_KEY: 'aNHM7hSiOrLj6SJKS',              // Replace with your public key
};
```

## Step 5: Test the Forms

1. Start your development server: `npm run dev`
2. Navigate to the contact page
3. Fill out and submit the contact form
4. Navigate to any page with the demo request form
5. Fill out and submit the demo request form
6. Check your email to confirm the emails are being sent

## Troubleshooting

### Common Issues:

1. **"Failed to send email"**: Check your service ID, template IDs, and public key
2. **"Network error"**: Ensure you have internet connection and EmailJS service is active
3. **Template variables not showing**: Make sure variable names in templates match the ones in the code

### Template Variables Reference:

**Contact Form Variables:**
- `{{from_name}}` - User's full name
- `{{from_email}}` - User's email
- `{{phone}}` - User's phone number
- `{{company}}` - User's company
- `{{product}}` - Product interest
- `{{message}}` - User's message
- `{{to_name}}` - Recipient name (DocSynecX Team)

**Demo Request Variables:**
- `{{from_name}}` - User's name
- `{{from_email}}` - User's email
- `{{company}}` - User's company
- `{{message}}` - User's requirements
- `{{to_name}}` - Recipient name (DocSynecX Demo Team)

## Security Notes

- Never commit your actual EmailJS credentials to version control
- Consider using environment variables for production
- The public key is safe to expose in client-side code
- EmailJS has rate limiting to prevent abuse

## Support

For EmailJS-specific issues, refer to:
- [EmailJS Documentation](https://www.emailjs.com/docs/)
- [EmailJS Support](https://www.emailjs.com/support/)
