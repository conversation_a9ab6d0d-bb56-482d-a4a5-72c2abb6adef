import React from 'react';

interface InteractiveKeyValueListProps {
  data: Record<string, string>;
  heading: string;
  selectedField: string | null;
  onFieldClick: (field: string) => void;
}

const InteractiveKeyValueList: React.FC<InteractiveKeyValueListProps> = ({
  data,
  heading,
  selectedField,
  onFieldClick
}) => {
  return (
    <div className="mb-6">
      <h3 className="text-lg font-bold text-gray-900 mb-4 border-b border-gray-200 pb-2">
        {heading}
      </h3>
      <div className="space-y-3">
        {Object.entries(data).map(([key, value]) => (
          <div
            key={key}
            className={`p-3 rounded-lg border border-l-4 transition-all duration-300 cursor-pointer ${
              selectedField === key
                ? 'border-green-500 border-l-green-500 bg-green-50 shadow-md'
                : 'border-gray-200 border-l-green-400 bg-gray-50 hover:border-green-300 hover:bg-green-25'
            }`}
            onClick={() => onFieldClick(key === selectedField ? '' : key)}
          >
            <div className="flex justify-between items-start">
              <span className="text-sm font-medium text-gray-600 mb-1 block">
                {key}:
              </span>
              <div className={`text-sm font-semibold transition-colors ${
                selectedField === key ? 'text-green-700' : 'text-gray-900'
              }`}>
                {value}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default InteractiveKeyValueList;
