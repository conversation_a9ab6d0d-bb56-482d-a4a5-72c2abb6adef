import emailjs from '@emailjs/browser';

// EmailJS configuration
export const EMAILJS_CONFIG = {
  SERVICE_ID: 'service_dbf6de6',        // Replace with your service ID
  TEMPLATE_ID_CONTACT: 'template_0bfyahq', // Replace with contact template ID
  TEMPLATE_ID_DEMO: 'template_p505oeb',       // Replace with demo template ID
  PUBLIC_KEY: 'aNHM7hSiOrLj6SJKS',              // Replace with your public key
};

// Initialize EmailJS
export const initEmailJS = () => {
  emailjs.init(EMAILJS_CONFIG.PUBLIC_KEY);
};

// Send contact form email
export const sendContactEmail = async (formData: {
  fullName: string;
  email: string;
  phone: string;
  company: string;
  product: string;
  description: string;
}) => {
  try {
    const templateParams = {
      from_name: formData.fullName,
      from_email: formData.email,
      phone: formData.phone,
      company: formData.company,
      product: formData.product,
      message: formData.description,
      to_name: 'DocSynecX Team',
      reply_to: formData.email,
    };

    const response = await emailjs.send(
      EMAILJS_CONFIG.SERVICE_ID,
      EMAILJS_CONFIG.TEMPLATE_ID_CONTACT,
      templateParams
    );

    return { success: true, response };
  } catch (error) {
    console.error('Failed to send contact email:', error);
    return { success: false, error };
  }
};

// Send demo request email
export const sendDemoEmail = async (formData: {
  name: string;
  email: string;
  phone: string;
  company: string;
  message: string;
}) => {
  try {
    const templateParams = {
      from_name: formData.name,
      from_email: formData.email,
      phone: formData.phone,
      company: formData.company,
      message: formData.message,
      to_name: 'DocSynecX Demo Team',
      reply_to: formData.email,
    };

    const response = await emailjs.send(
      EMAILJS_CONFIG.SERVICE_ID,
      EMAILJS_CONFIG.TEMPLATE_ID_DEMO,
      templateParams
    );

    return { success: true, response };
  } catch (error) {
    console.error('Failed to send demo email:', error);
    return { success: false, error };
  }
};
