import { useState, useEffect } from "react";
import { Check, Star, Shield, CheckCircle, ArrowRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import Navbar from "@/components/Navbar";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import FeatureComparison from "@/components/pricing/FeatureComparison";
import Footer from "@/components/Footer";
import { useNavigate } from "react-router-dom";

const pricingPlans = [
  {
    name: "Free",
    description: "For Individuals",
    monthlyPrice: 0,
    yearlyPrice: 0,
    pages: "50 pages",
    monthlyPages: "50 pages",
    yearlyPages: "50 pages",
    features: [
      "Basic Image Quality Check",
      "Multi-format support",
      "Basic field extraction",
      "Table extraction",
      "Export to CSV/JSON",
    ],
    buttonText: "14 Days Free Trial",
    variant: "secondary",
  },
  {
    name: "Explore",
    description: "For Small Teams",
    monthlyPrice: 85.00,
    yearlyPrice: 77.00,
    pages: "1000 pages/month",
    monthlyPages: "1000 pages/month",
    yearlyPages: "12000 pages/year",
    features: [
      "Auto Image Quality Check",
      "Auto Orientation",
      "Document reviewer",
      "Pre-trained templates",
      "Export to CSV/JSON",
      "Email support",
      "Cancel anytime",
    ],
    buttonText: "14 Days Free Trial",
    variant: "default",
  },
  {
    name: "Grow",
    description: "For Growing Businesses",
    monthlyPrice: 225.00,
    yearlyPrice: 205.00,
    pages: "3000 pages/month",
    monthlyPages: "3000 pages/month",
    yearlyPages: "36000 pages/year",
    features: [
      "API Access",
      "Auto document splitting",
      "Complex table extraction",
      "AI-powered data parsing",
      "Dashboard analytics",
      "Basic support",
      "Team collaboration",
      "Cancel anytime"
    ],
    buttonText: "14 Days Free Trial",
    variant: "default",
    popular: true,
  },
  {
    name: "Premium",
    description: "For Mature Businesses",
    monthlyPrice: 450.00,
    yearlyPrice: 405.00,
    pages: "6000 pages/month",
    monthlyPages: "6000 pages/month",
    yearlyPages: "72000 pages/year",
    features: [
      "API Access",
      "AI document categorization",
      "Custom models upto 5",
      "Pre-built integrations",
      "Confidence scoring",
      "Batch document processing",
      "Advanced analytics",
      "Multi-factor authentication",
      "Priority support",
      "Team collaboration",
      "Cancel anytime",

    ],
    buttonText: "14 Days Free Trial",
    variant: "default",
  },
  {
    name: "Enterprise",
    description: "For Large Enterprises",
    monthlyPrice: "Custom Pricing",
    yearlyPrice: "Custom Pricing",
    pages: "Custom Credits",
    monthlyPages: "Custom Credits",
    yearlyPages: "Custom Credits",
    features: [
      "Unlimited users",
      "Enterprise API access",
      "Unlimited invoice processing",
      "Custom fine-tuned models",
      "Custom workflows",
      "Custom integrations",
      "Custom templates",
      "Advanced data parsing",
      "Role-based access",
      "Auto classification documents",
      "Advanced dashboard",
      "Enterprise-grade security",
      "Dedicated account manager",
      "24/7 dedicated support",
      "On-premise deployment",
    ],
    buttonText: "Request a Call",
    variant: "default",
  },
];

const Pricing = () => {
  const [billingCycle, setBillingCycle] = useState("yearly");
  const navigate = useNavigate();

  // Set page title when component mounts
  useEffect(() => {
    document.title = "Document AI Pricing - DocSynecX";
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100">
      <Navbar />

      <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight px-4">
            Document AI{" "}
            <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
              Pricing Plans
            </span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed px-4">
            Choose the perfect plan for your document processing. Scale from individual use to enterprise-grade document AI.
          </p>
        </div>
      </section>

      <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            Flexible Pricing for Every Need
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-4">
            Choose the perfect plan for your document processing.
          </p>
          <div className="flex items-center justify-center space-x-4 mt-6">
            <span className={`text-lg ${billingCycle === "monthly" ? "text-teal-600 font-semibold" : "text-gray-500"}`}>Monthly</span>
            <Switch
              checked={billingCycle === "yearly"}
              onCheckedChange={() => setBillingCycle(billingCycle === "monthly" ? "yearly" : "monthly")}
            />
            <span className={`text-lg ${billingCycle === "yearly" ? "text-teal-600 font-semibold" : "text-gray-500"}`}>
              Yearly (Save 15%)
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {pricingPlans.slice(0, pricingPlans.length - 2).map((plan) => (
            <Card
              key={plan.name}
              className={`relative flex flex-col h-full hover:shadow-2xl transition-all duration-300 border-2 ${plan.popular ? "border-teal-500 bg-gradient-to-br from-teal-50 to-green-50" : "border-gray-200"
                }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-teal-600 to-green-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                    Most Popular
                  </span>
                </div>
              )}
              <div className="flex-1 flex flex-col p-6">
                <CardHeader className="text-center pb-6">
                  <CardTitle className="text-2xl font-bold text-gray-900">{plan.name}</CardTitle>
                  <CardDescription className="text-gray-600 mt-2 text-base">{plan.description}</CardDescription>
                  <div className="mt-6">
                    <span className="text-4xl font-bold text-gray-900">
                      {typeof (billingCycle === "monthly" ? plan.monthlyPrice : plan.yearlyPrice) === 'string'
                        ? (billingCycle === "monthly" ? plan.monthlyPrice : plan.yearlyPrice)
                        : `$${billingCycle === "monthly" ? plan.monthlyPrice : plan.yearlyPrice}`}
                    </span>
                    {typeof (billingCycle === "monthly" ? plan.monthlyPrice : plan.yearlyPrice) !== 'string' && (
                      <span className="text-gray-600 ml-2 text-base">/{billingCycle === "monthly" ? "month" : "month"}</span>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    {billingCycle === "monthly" ? plan.monthlyPages : plan.yearlyPages}
                  </p>
                </CardHeader>
                <CardContent className="pt-0">
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-center">
                        <Check className="w-5 h-5 text-teal-500 mr-3 flex-shrink-0" />
                        <span className="text-gray-700 text-base">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </div>
              <div className="mt-auto p-6 pt-0">
                <Button
                  className={`w-full text-base font-semibold py-3 ${plan.variant === "secondary"
                    ? "bg-gray-200 text-gray-800 hover:bg-gray-300"
                    : "bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 text-white"
                    }`}
                  onClick={() => {
                    if (plan.name === "Enterprise") {
                      navigate("/contact");
                    } else {
                      window.open("https://app.docsynecx.com/signin/", "_blank");
                    }
                  }}
                >
                  {plan.buttonText}
                </Button>
              </div>
            </Card>
          ))}

          {/* ✅ Updated Responsive Bottom 2 Cards */}
          {pricingPlans.slice(-2).map((p) => (
            <Card
              key={p.name}
              className={`relative flex flex-col h-full hover:shadow-2xl transition-all duration-300 border-2 ${p.popular ? "border-teal-500 bg-gradient-to-br from-teal-50 to-green-50" : "border-gray-200"
                } w-full`}
            >
              <div className="flex-1 flex flex-col p-6">
                <CardHeader className="text-center pb-6">
                  <CardTitle className="text-2xl font-bold text-gray-900">{p.name}</CardTitle>
                  <CardDescription className="text-gray-600 mt-2 text-base">{p.description}</CardDescription>
                  <div className="mt-6">
                    <span className="text-4xl font-bold text-gray-900">
                      {typeof (billingCycle === "monthly" ? p.monthlyPrice : p.yearlyPrice) === 'string'
                        ? (billingCycle === "monthly" ? p.monthlyPrice : p.yearlyPrice)
                        : `$${billingCycle === "monthly" ? p.monthlyPrice : p.yearlyPrice}`}
                    </span>
                    {typeof (billingCycle === "monthly" ? p.monthlyPrice : p.yearlyPrice) !== 'string' && (
                      <span className="text-gray-600 ml-2 text-base">/{billingCycle === "monthly" ? "month" : "year"}</span>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    {billingCycle === "monthly" ? p.monthlyPages : p.yearlyPages}
                  </p>
                </CardHeader>
                <CardContent className="pt-0">
                  <ul className="space-y-3 mb-8">
                    {p.features.map((feature) => (
                      <li key={feature} className="flex items-center">
                        <Check className="w-5 h-5 text-teal-500 mr-3 flex-shrink-0" />
                        <span className="text-gray-700 text-base">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </div>
              <div className="mt-auto p-6 pt-0">
                <Button
                  className={`w-full text-base font-semibold py-3 ${p.variant === "secondary"
                    ? "bg-gray-200 text-gray-800 hover:bg-gray-300"
                    : "bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 text-white"
                    }`}
                  onClick={() => {
                    if (p.name === "Enterprise") {
                      navigate("/contact");
                    } else {
                      window.open("https://app.docsynecx.com/signin/", "_blank");
                    }
                  }}
                >
                  {p.buttonText}
                </Button>
              </div>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-600 mb-4 text-base px-4">All plans include our advanced document processing technology and document AI capabilities</p>
          <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-gray-600 px-4">
            <div className="flex items-center">
              <Star className="w-4 h-4 text-yellow-500 mr-2" />
              <span>97.0% accuracy</span>
            </div>
            <div className="flex items-center">
              <Shield className="w-4 h-4 text-teal-500 mr-2" />
              <span>Enterprise security</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="w-4 h-4 text-teal-500 mr-2" />
              <span>API integration</span>
            </div>
          </div>
        </div>
      </section>

      <FeatureComparison billingCycle={billingCycle} />

      <DemoRequestForm />

      <section className="bg-gradient-to-r from-teal-600 to-green-600 py-16 lg:py-20">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <div className="max-w-3xl mx-auto text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 px-4">
              Ready to Get Started with DocSynecX?
            </h2>
            <p className="text-xl text-teal-100 mb-8 px-4">
              Join thousands of businesses already using our advanced OCR and document AI platform
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4">
              <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg w-full sm:w-auto" asChild>
                <a href="https://app.docsynecx.com/signin/" target="_blank" rel="noopener noreferrer">
                  Start Free Trial
                  <ArrowRight className="ml-2 w-5 h-5" />
                </a>
              </Button>
              <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg w-full sm:w-auto" onClick={() => navigate("/contact")}>
                Contact Sales Team
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Pricing;
