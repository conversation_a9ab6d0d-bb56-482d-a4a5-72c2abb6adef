import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Receipt, 
  CreditCard, 
  FileCheck, 
  Building, 
  Truck,
  Hospital,
  Briefcase,
  Calendar
} from "lucide-react";
import { useNavigate } from 'react-router-dom';

const DocumentProcessingSolutions = () => {
  const navigate = useNavigate();

  const solutions = [
    {
      icon: Receipt,
      title: "Invoice Processing",
      description: "Automate invoice data extraction, validation, and approval workflows",
      features: ["Vendor identification", "Line item extraction", "Tax calculation", "Duplicate detection"],
      industry: "Finance"
    },
    {
      icon: CreditCard,
      title: "Financial Documents",
      description: "Process bank statements, tax forms, and financial reports with precision",
      features: ["Transaction categorization", "Balance verification", "Compliance checking", "Audit trails"],
      industry: "Banking"
    },
    {
      icon: FileCheck,
      title: "Contract Management",
      description: "Extract key terms, dates, and obligations from legal documents",
      features: ["Clause identification", "Date tracking", "Risk assessment", "Version control"],
      industry: "Legal"
    },
    {
      icon: Building,
      title: "Real Estate Documents",
      description: "Process property documents, leases, and mortgage applications",
      features: ["Property details", "Lease terms", "Document verification", "Compliance checks"],
      industry: "Real Estate"
    },
    {
      icon: Truck,
      title: "Logistics & Shipping",
      description: "Handle bills of lading, customs forms, and shipping manifests",
      features: ["Shipment tracking", "Customs clearance", "Route optimization", "Delivery confirmation"],
      industry: "Logistics"
    },
    {
      icon: Hospital,
      title: "Healthcare Records",
      description: "Digitize patient records, insurance claims, and medical forms",
      features: ["Patient data extraction", "Insurance verification", "HIPAA compliance", "Medical coding"],
      industry: "Healthcare"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Industry-Specific Solutions
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our intelligent document processing adapts to your industry's unique requirements 
            and document types, delivering tailored solutions that drive efficiency.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {solutions.map((solution, index) => (
            <Card key={index} className="h-full hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <solution.icon className="h-6 w-6 text-green-600" />
                  </div>
                  <Badge variant="secondary">{solution.industry}</Badge>
                </div>
                <CardTitle className="text-xl mb-2">{solution.title}</CardTitle>
                <CardDescription className="text-gray-600">
                  {solution.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {solution.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Don't see your industry?
            </h3>
            <p className="text-gray-600 mb-6">
              Our flexible AI platform can be customized for any document processing need.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                className="bg-gradient-to-r from-teal-600 to-green-600 text-white px-6 py-3 rounded-lg hover:from-teal-700 hover:to-green-700 transition-colors"
                onClick={() => navigate('/contact')}
              >
                Contact Sales
              </button>
              <button 
                className="border-2 border-green-600 text-green-700 px-6 py-3 rounded-lg hover:bg-green-50 transition-colors"
                onClick={() => navigate('/solutions')}
              >
                Custom Solutions
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DocumentProcessingSolutions;