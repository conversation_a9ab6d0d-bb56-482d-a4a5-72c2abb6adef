// convert-docx-to-blog.js
const mammoth = require("mammoth");
const fs = require("fs");
const path = require("path");

// Helper to wrap HTML as React.createElement(React.Fragment, null, ...)
function htmlToReactFragment(html) {
  // This wraps the HTML in a React.Fragment using dangerouslySetInnerHTML
  return `React.createElement(React.Fragment, null,
    React.createElement("div", { dangerouslySetInnerHTML: { __html: \`${html}\` } })
  )`;
}

async function convertDocx(filePath, meta) {
  const { value: html } = await mammoth.convertToHtml({ path: filePath });
  const reactContent = htmlToReactFragment(html);

  // Output in the same format as your blogData.ts
  const blogObject = `
{
  id: "${meta.id}",
  title: "${meta.title}",
  excerpt: "${meta.excerpt}",
  featuredImage: "${meta.featuredImage}",
  content: ${reactContent},
  author: "${meta.author}",
  authorRole: "${meta.authorRole}",
  authorAvatar: "${meta.authorAvatar}",
  date: "${meta.date}",
  readTime: "${meta.readTime}",
  category: "${meta.category}",
  tags: ${JSON.stringify(meta.tags)},
  views: 0,
  likes: 0,
  comments: 0,
  bookmarks: 0
},
`;

  fs.writeFileSync(
    path.join(__dirname, "blogObject.txt"),
    blogObject,
    "utf-8"
  );
  console.log("Blog object generated in blogObject.txt");
}

// Example usage
convertDocx("my-blog.docx", {
  id: "8",
  title: "My New Blog Post",
  excerpt: "Short summary here.",
  featuredImage: "/BlogImages/8.png",
  author: "Your Name",
  authorRole: "Your Role",
  authorAvatar: "YN",
  date: "2025-07-12",
  readTime: "5 min read",
  category: "Category",
  tags: ["Tag1", "Tag2"]
});