import LogisticsTradeHero from "@/components/logistics-trade/LogisticsTradeHero";
import LogisticsTradeDocuments from "@/components/logistics-trade/LogisticsTradeDocuments";
import LogisticsTradeExtraction from "@/components/logistics-trade/LogisticsTradeExtraction";
import LogisticsTradeDataExtraction from "@/components/logistics-trade/LogisticsTradeDataExtraction";
import LogisticsTradeCTA from "@/components/logistics-trade/LogisticsTradeCTA";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import { useEffect } from "react";
const LogisticsTrade = () => {
  useEffect(() => {
    document.title = "Automate Bill of Lading, Shipping & Customs Docs - Logistics AI by DocSynecX";
  }, []);

  return (

    
    <div className="min-h-screen bg-gradient-to-br from-background via-primary/5 to-secondary/10">
      <Navbar />
      <LogisticsTradeHero />
      <LogisticsTradeDocuments />
      <LogisticsTradeExtraction />
      <LogisticsTradeDataExtraction />
      <LogisticsTradeCTA />
      <DemoRequestForm />
      <Footer/>
    </div>
  );
};

export default LogisticsTrade;