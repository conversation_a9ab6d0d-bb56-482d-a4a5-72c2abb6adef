import { Button } from "@/components/ui/button";
import { ArrowRight, Play } from "lucide-react";
import { useNavigate } from "react-router-dom";

const DocumentProcessingCTA = () => {
  const navigate = useNavigate();

  const handleWatchDemo = () => {
    navigate('/');
    // After navigation, scroll to video section and auto-play
    setTimeout(() => {
      const videoSection = document.getElementById("ai-demo-video");
      if (videoSection) {
        videoSection.scrollIntoView({ behavior: "smooth" });
        
        // Auto-play video after scrolling
        setTimeout(() => {
          const video = document.getElementById("demo-video") as HTMLVideoElement | null;
          if (video) {
            video.play?.();
          }
        }, 1000);
      }
    }, 100);
  };

  return (
    <section className="py-20 bg-gradient-to-r from-teal-600 to-green-600">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
          Ready to Transform Your Document Processing?
        </h2>
        
        <p className="text-xl text-green-100 mb-8 leading-relaxed max-w-2xl mx-auto">
          Join thousands of companies already using our intelligent document processing 
          to reduce manual work, improve accuracy, and accelerate business processes.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Button
            size="lg"
            className="bg-white text-teal-700 hover:bg-green-50 group"
            onClick={() => window.open('https://app.docsynecx.com/signin/', '_blank')}
          >
            Start Free Trial
            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="border-white text-white bg-transparent hover:bg-white hover:text-teal-700 transition-colors"
            style={{ borderWidth: 2 }}
            onClick={handleWatchDemo}
          >
            <Play className="w-4 h-4 mr-2" />
            Watch Demo
          </Button>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">14-Day</div>
            <div className="text-green-100">Free Trial</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">No Setup</div>
            <div className="text-green-100">Fees Required</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">24/7</div>
            <div className="text-green-100">Expert Support</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DocumentProcessingCTA;