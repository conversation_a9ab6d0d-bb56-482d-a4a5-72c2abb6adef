import { useState, useEffect } from "react";
import { Upload, Brain, Download, ArrowRight, CheckCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const PlatformWorkflow = () => {
  const [activeStep, setActiveStep] = useState(0);

  const workflowSteps = [
    {
      id: 1,
      icon: Upload,
      title: "Upload Documents",
      description: "Drag-and-drop interface with batch processing and automatic format detection",
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      textColor: "text-blue-600"
    },
    {
      id: 2,
      icon: Brain,
      title: "AI Processing",
      description: "Advanced machine learning models for extraction, analysis, and classification",
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      textColor: "text-purple-600"
    },
    {
      id: 3,
      icon: Download,
      title: "Export & API",
      description: "Multiple export formats and robust APIs for system integration",
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      textColor: "text-green-600"
    }
  ];

  // Auto-advance animation
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % workflowSteps.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [workflowSteps.length]);

  return (
    <section className="bg-gradient-to-br from-slate-50 via-teal-50 to-green-100 py-12 sm:py-16 lg:py-20">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            How DocSynecX Works
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-4">
            Experience the power of AI-driven document processing in three simple steps
          </p>
        </div>

        {/* Workflow Steps */}
        <div className="max-w-6xl mx-auto">
          {/* Desktop View */}
          <div className="hidden lg:block">
            <div className="relative">
              {/* Connection Lines */}
              <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gray-200 transform -translate-y-1/2 z-0"></div>
              
              {/* Animated Progress Line */}
              <div 
                className="absolute top-1/2 left-0 h-0.5 bg-gradient-to-r from-teal-500 to-green-500 transform -translate-y-1/2 z-10 transition-all duration-1000 ease-in-out"
                style={{ width: `${((activeStep + 1) / workflowSteps.length) * 100}%` }}
              ></div>

              <div className="grid grid-cols-3 gap-8 relative z-20">
                {workflowSteps.map((step, index) => {
                  const Icon = step.icon;
                  const isActive = index === activeStep;
                  const isCompleted = index < activeStep;

                  return (
                    <div key={step.id} className="text-center">
                      {/* Step Circle */}
                      <div 
                        className={`w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center transition-all duration-500 transform ${
                          isActive 
                            ? `bg-gradient-to-r ${step.color} scale-110 shadow-lg` 
                            : isCompleted
                            ? 'bg-gradient-to-r from-teal-500 to-green-500'
                            : 'bg-white border-2 border-gray-200'
                        }`}
                        onClick={() => setActiveStep(index)}
                        style={{ cursor: 'pointer' }}
                      >
                        {isCompleted ? (
                          <CheckCircle className="w-8 h-8 text-white" />
                        ) : (
                          <Icon className={`w-8 h-8 ${isActive ? 'text-white' : 'text-gray-400'}`} />
                        )}
                      </div>

                      {/* Step Content */}
                      <Card className={`transition-all duration-500 transform ${isActive ? 'scale-105 shadow-xl' : 'hover:shadow-lg'}`}>
                        <CardContent className="p-6">
                          <h3 className={`text-lg font-bold mb-2 ${isActive ? step.textColor : 'text-gray-700'}`}>
                            {step.title}
                          </h3>
                          <p className="text-gray-600 text-sm">
                            {step.description}
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Mobile View */}
          <div className="lg:hidden space-y-6">
            {workflowSteps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === activeStep;

              return (
                <div key={step.id} className="flex items-start space-x-4">
                  <div 
                    className={`w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 transition-all duration-500 ${
                      isActive 
                        ? `bg-gradient-to-r ${step.color} scale-110` 
                        : 'bg-white border-2 border-gray-200'
                    }`}
                  >
                    <Icon className={`w-6 h-6 ${isActive ? 'text-white' : 'text-gray-400'}`} />
                  </div>
                  
                  <Card className={`flex-1 transition-all duration-500 ${isActive ? 'shadow-lg border-l-4 border-teal-500' : ''}`}>
                    <CardContent className="p-4">
                      <h3 className={`text-base font-bold mb-1 ${isActive ? step.textColor : 'text-gray-700'}`}>
                        {step.title}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {step.description}
                      </p>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>
        </div>

        {/* Process Indicators */}
        <div className="flex justify-center mt-8 space-x-2">
          {workflowSteps.map((_, index) => (
            <button
              key={index}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === activeStep 
                  ? 'bg-teal-500 scale-125' 
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
              onClick={() => setActiveStep(index)}
            />
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-12">
          <div className="bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Streamline Your Document Processing?
            </h3>
            <p className="text-gray-600 mb-6">
              Join thousands of businesses automating their workflows with DocSynecX
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                className="bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 flex items-center justify-center"
                onClick={() => {
                  const form = document.getElementById('demo-request-form');
                  if (form) form.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Request Demo
                <ArrowRight className="ml-2 w-4 h-4" />
              </button>
              <button 
                className="border border-teal-600 text-teal-600 hover:bg-teal-50 px-6 py-3 rounded-lg font-medium transition-all duration-300"
                onClick={() => window.open("https://app.docsynecx.com/signin/", "_blank")}
              >
                Try Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PlatformWorkflow;
