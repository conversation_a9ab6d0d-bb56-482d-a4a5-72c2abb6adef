import { useState, useRef } from "react";
import { FileText, ArrowRight, CheckCircle, Upload, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import KeyValueList from "../components/KeyValueList";

const invoiceData = {
  "Basic Information": {
    "Invoice Number": "EM089G2500549",
    "Issue Date": "26/02/2025"
  },
  "Buyer Detail": {
    "Address": "PAYYANNOOR,PIN:670307",
    "GSTNo": "32AAAAT4067B1ZZ",
    "Name": "PAYYANNUR CO OPERATIVE STORE"
  },
  "Seller Detail": {
    "Address": "PO Box No: 15, Eastern Valley, Adimali",
    "GSTNo": "32AAACM9878K1ZY",
    "Name": "Orkla India Private Limited"
  },
  "GST/Amount": {
    "CGST": "69.20",
    "SGST": "69.20"
  },
  "Total Amount": {
    "Gross Total": "2907.00",
    "Inwords": "Rupees Two Thousand Nine Hundred And Seven Only",
    "Sub Total": "2768.21"
  }
};

const productsData = [
  {
    "Amount": "838.00",
    "Product Name": "Chicken Masala Powder 100g Pouch",
    "Quantity": "2",
    "Tax Amount": "41.06",
    "Tax Percentage": "5.0",
    "Unit Price": "419.00"
  },
  {
    "Amount": "1828.60",
    "Product Name": "Black Pepper Powder 100g Pouch",
    "Quantity": "2",
    "Tax Amount": "89.60",
    "Tax Percentage": "5.0",
    "Unit Price": "914.30"
  },
  {
    "Amount": "158.10",
    "Product Name": "Fenugreek Powder 100 g Pouch",
    "Quantity": "1",
    "Tax Amount": "7.75",
    "Tax Percentage": "5.0",
    "Unit Price": "158.10"
  }
];

const invoiceJsonData = [
  {
    "Basic Information": {
      "Invoice Number": "EM089G2500549",
      "Issue Date": "26/02/2025"
    },
    "Buyer Detail": {
      "Address": "PAYYANNOOR,PIN:670307",
      "GSTNo": "32AAAAT4067B1ZZ",
      "Name": "PAYYANNUR CO OPERATIVE STORE"
    },
    "GST/Amount": {
      "CGST": "69.20",
      "SGST": "69.20"
    },
    "Products": [
      {
        "Amount": "838.00",
        "Product Name": "Chicken Masala Powder 100g Pouch",
        "Quantity": "2",
        "Tax Amount": "41.06",
        "Tax Percentage": "5.0",
        "Unit Price": "419.00"
      },
      {
        "Amount": "1828.60",
        "Product Name": "Black Pepper Powder 100g Pouch",
        "Quantity": "2",
        "Tax Amount": "89.60",
        "Tax Percentage": "5.0",
        "Unit Price": "914.30"
      },
      {
        "Amount": "158.10",
        "Product Name": "Fenugreek Powder 100 g Pouch",
        "Quantity": "1",
        "Tax Amount": "7.75",
        "Tax Percentage": "5.0",
        "Unit Price": "158.10"
      }
    ],
    "Seller Detail": {
      "Address": "PO Box No: 15, Eastern Valley, Adimali",
      "GSTNo": "32AAACM9878K1ZY",
      "Name": "Orkla India Private Limited"
    },
    "Total Amount": {
      "Gross Total": "2907.00",
      "Inwords": "Rupees Two Thousand Nine Hundred And Seven Only",
      "Sub Total": "2768.21"
    }
  }
];

const InvoiceOCR = () => {
  const [file, setFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [selectedField, setSelectedField] = useState<string | null>(null);
  const [showJson, setShowJson] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const [visibleSections, setVisibleSections] = useState({
    basicInfo: true,
    buyerDetail: true,
    sellerDetail: true,
    gstAmount: true,
    products: true,
    totalAmount: true
  });

  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleClick = () => {
    inputRef.current?.click();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100">
      <Navbar />

      {/* Demo Section Header */}
      <section className="container mx-auto px-4 sm:px-6 py-12">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            AI-Powered Invoice OCR Demo
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Upload your invoice or try our sample to experience 97% accurate data extraction in real-time
          </p>
          <div className="inline-flex items-center space-x-4 bg-white rounded-lg p-4 shadow-sm">
            <div className="flex items-center text-gray-600">
              <Check className="w-4 h-4 text-green-500 mr-2" />
              Instant Results
            </div>
            <div className="flex items-center text-gray-600">
              <Check className="w-4 h-4 text-green-500 mr-2" />
              Multiple Formats
            </div>
            <div className="flex items-center text-gray-600">
              <Check className="w-4 h-4 text-green-500 mr-2" />
              Free to Try
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 max-w-6xl mx-auto">
          {/* Left: File Upload Area */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <form className="h-full flex flex-col" onSubmit={(e) => e.preventDefault()}>
              <div 
                className={`flex-1 flex flex-col items-center justify-center border-2 border-dashed rounded-xl p-8 transition-colors
                  ${dragActive ? 'border-green-400 bg-green-50' : 'border-gray-300 hover:border-gray-400'}
                  ${file ? 'bg-green-50 border-green-400' : ''}`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={handleClick}
              >
                <input
                  ref={inputRef}
                  type="file"
                  className="hidden"
                  onChange={handleFileChange}
                  accept="image/*,.pdf"
                />
                
                {file ? (
                  <div className="text-center">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                    <p className="text-lg font-medium text-gray-900">{file.name}</p>
                    <p className="text-sm text-gray-500 mt-1">
                      {(file.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                    <button
                      className="mt-4 text-sm text-red-600 hover:text-red-700"
                      onClick={(e) => {
                        e.stopPropagation();
                        setFile(null);
                      }}
                    >
                      Remove file
                    </button>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Upload className="w-8 h-8 text-gray-400" />
                    </div>
                    <p className="text-lg font-medium text-gray-900">
                      Drop your invoice here
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Supports PDF, PNG, JPEG up to 10MB
                    </p>
                  </div>
                )}
              </div>

              {file && (
                <Button 
                  className="mt-4 bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700"
                >
                  Process Invoice
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              )}

              <div className="mt-6 text-center">
                <button
                  type="button"
                  className="text-teal-600 hover:text-teal-700 text-sm font-medium"
                  onClick={() => {/* Load sample */}}
                >
                  Or try with a sample invoice
                </button>
              </div>
            </form>
          </div>

          {/* Right: Extracted Data */}
          <div className="bg-white rounded-2xl shadow-lg p-0 min-h-[600px] max-h-[600px] flex flex-col relative">
            {/* Toggle Buttons */}
            <div className="absolute top-4 right-4 z-20 flex gap-2">
              <button
                className={`px-3 py-1 text-sm font-medium rounded shadow border transition ${
                  !showJson 
                    ? 'bg-teal-600 text-white border-teal-600' 
                    : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'
                }`}
                onClick={() => setShowJson(false)}
              >
                List View
              </button>
              <button
                className={`px-3 py-1 text-sm font-medium rounded shadow border transition ${
                  showJson 
                    ? 'bg-teal-600 text-white border-teal-600' 
                    : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'
                }`}
                onClick={() => setShowJson(true)}
              >
                JSON View
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-8">
              {showJson ? (
                <div>
                  <div className="mb-4 font-bold text-lg text-gray-700">JSON Data</div>
                  <pre className="bg-white text-green-600 rounded-lg p-4 text-xs sm:text-sm overflow-x-auto max-h-[60vh] whitespace-pre-wrap border border-gray-200">
                    {JSON.stringify(invoiceJsonData, null, 2)}
                  </pre>
                </div>
              ) : (
                <>
                  {visibleSections.basicInfo && (
                    <KeyValueList data={invoiceData["Basic Information"]} heading="Basic Information" />
                  )}
                  {visibleSections.sellerDetail && (
                    <KeyValueList data={invoiceData["Seller Detail"]} heading="Seller Detail" />
                  )}
                  {visibleSections.buyerDetail && (
                    <KeyValueList data={invoiceData["Buyer Detail"]} heading="Buyer Detail" />
                  )}
                  {visibleSections.gstAmount && (
                    <KeyValueList data={invoiceData["GST/Amount"]} heading="GST/Amount" />
                  )}
                  {visibleSections.totalAmount && (
                    <KeyValueList data={invoiceData["Total Amount"]} heading="Total Amount" />
                  )}
                </>
              )}
            </div>

            {/* Action Buttons */}
            <div className="border-t border-gray-200 p-4 bg-gray-50">
              <div className="flex justify-end space-x-3">
                <Button variant="outline" disabled={!file}>
                  Export Data
                </Button>
                <Button 
                  className="bg-teal-600 hover:bg-teal-700"
                  disabled={!file}
                >
                  Save & Process
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default InvoiceOCR;