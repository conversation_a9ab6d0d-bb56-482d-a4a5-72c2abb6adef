const ResourcesHero = () => {
  return (
    <section className="container mx-auto px-6 py-20 text-center">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
          Developer{" "}
          <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
            Resources & APIs
          </span>
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
          Comprehensive API documentation, integration guides, and developer tools for implementing 
          DocSynecX in your applications and workflows
        </p>
      </div>
    </section>
  );
};

export default ResourcesHero;
