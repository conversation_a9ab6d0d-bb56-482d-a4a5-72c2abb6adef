import { FileText, Linkedin, Instagram, Twitter, Youtube, Mail } from "lucide-react";
import { Link, useLocation } from "react-router-dom";

const Footer = () => {
  const location = useLocation();
  const isActive = (path: string) => location.pathname === path;
  return (
    <footer className="bg-gray-900 text-white py-8 sm:py-12">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-6 sm:gap-8 text-xs sm:text-sm">
          <div>
            <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                <Link to="/" className="flex items-center text-white">
                    <img src="/Docsynecx-Logo-footer.svg" alt="Docsynecx Logo" className="h-12 w-auto" />
                </Link>
            </div>
            <p className="text-gray-400 text-xs sm:text-sm">
              The Leading document AI platform for intelligent document processing and automated data extraction.
            </p>
            
            {/* Social Media Links */}
            <div className="flex space-x-4 mt-4">
              <a 
                href="https://www.linkedin.com/showcase/docsynecx/posts/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a 
                href="https://www.instagram.com/DocSynecX/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Instagram className="w-5 h-5" />
              </a>
              {/* <a 
                href="https://twitter.com/docsynecx" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Twitter className="w-5 h-5" />
              </a> */}
              <a 
                href="https://www.youtube.com/@Docsynecx" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Youtube className="w-5 h-5" />
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Mail className="w-5 h-5" />
              </a>
            </div>
          </div>
          <div>
            <h4 className="font-semibold mb-3 sm:mb-4 text-xs sm:text-sm">Document Automation Guides</h4>
            <ul className="space-y-2 text-gray-400 text-xs sm:text-sm">
              {/* <li><Link to="/platform" className="hover:text-white transition-colors">OCR API</Link></li> */}
              <li><Link to="/invoice-ocr" className="hover:text-white transition-colors">Invoice Processing</Link></li>
              <li><Link to="/document-processing" className="hover:text-white transition-colors">Document Processing</Link></li>
              {/* <li><Link to="/document-processing" className="hover:text-white transition-colors">Data Parsing</Link></li> */}
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-3 sm:mb-4 text-xs sm:text-sm">Solutions by Industry</h4>
            <ul className="space-y-2 text-gray-400 text-xs sm:text-sm">
              <li><Link to="/solutions#by-industry" className="hover:text-white transition-colors">Banking & Finance</Link></li>
              <li><Link to="/solutions#by-industry" className="hover:text-white transition-colors">Insurance</Link></li>
              <li><Link to="/solutions#by-industry" className="hover:text-white transition-colors">HealthCare</Link></li>
              <li><Link to="/logistics-trade" className="hover:text-white transition-colors">Logistics</Link></li>
              <li><Link to="/solutions#by-industry" className="hover:text-white transition-colors">Commercial Real Estate</Link></li>
              {/* <li><Link to="/solutions#by-industry" className="hover:text-white transition-colors">Human Resources & Employee Onboarding</Link></li> */}
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-3 sm:mb-4 text-xs sm:text-sm">Solutions by Use Case</h4>
            <ul className="space-y-2 text-gray-400 text-xs sm:text-sm">
              <li><Link to="/solutions#usecase" className="hover:text-white transition-colors">Finance & Accounting</Link></li>
              <li><Link to="/solutions#usecase" className="hover:text-white transition-colors">Account Payable</Link></li>
              <li><Link to="/solutions#usecase" className="hover:text-white transition-colors">Logistics & Supply Chain</Link></li>
              <li><Link to="/solutions#usecase" className="hover:text-white transition-colors">Healthcare</Link></li>
              <li><Link to="/solutions#usecase" className="hover:text-white transition-colors">Utility Bill Management</Link></li>
              <li><Link to="/solutions#usecase" className="hover:text-white transition-colors">KYC Customer Onboarding</Link></li>
            </ul>

             {/* <ul className="space-y-2 text-gray-400 text-xs sm:text-sm">
              <li><Link to="/solutions#usecase-invoice" className="hover:text-white transition-colors">Automated Invoice Processing</Link></li>
              <li><Link to="/solutions#usecase-ocr-ai" className="hover:text-white transition-colors">OCR AI Document Analysis</Link></li>
              <li><Link to="/solutions#usecase-trade" className="hover:text-white transition-colors">Trade Document OCR Processing</Link></li>
              <li><Link to="/solutions#usecase-account" className="hover:text-white transition-colors">Account Payable</Link></li>
              <li><Link to="/solutions#usecase-utility" className="hover:text-white transition-colors">Utility Bill Management</Link></li>
              <li><Link to="/solutions#usecase-kyc" className="hover:text-white transition-colors">KYC Customer Onboarding</Link></li>
            </ul> */}
          </div>
          <div>
            <h4 className="font-semibold mb-3 sm:mb-4 text-xs sm:text-sm">Company</h4>
            <ul className="space-y-2 text-gray-400 text-xs sm:text-sm">
              <li><Link to="/about" className={`${isActive('/about') ? 'text-teal-400 font-semibold' : 'hover:text-white transition-colors'}`}>About</Link></li>
              <li><Link to="/blog" className={`${isActive('/blog') ? 'text-teal-400 font-semibold' : 'hover:text-white transition-colors'}`}>Blog</Link></li>
              <li><Link to="/contact" className={`${isActive('/contact') ? 'text-teal-400 font-semibold' : 'hover:text-white transition-colors'}`}>Contact</Link></li>
              <li><Link to="/privacy-policy" className={`${isActive('/privacy-policy') ? 'text-teal-400 font-semibold' : 'hover:text-white transition-colors'}`}>Privacy Policy</Link></li>
              <li><Link to="/terms-of-service" className={`${isActive('/terms-of-service') ? 'text-teal-400 font-semibold' : 'hover:text-white transition-colors'}`}>Terms of Service</Link></li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-3 sm:mb-4 text-xs sm:text-sm">Support</h4>
            <ul className="space-y-2 text-gray-400 text-xs sm:text-sm">
              {/* <li><Link to="/resources" className="hover:text-white transition-colors">API Documentation</Link></li> */}
              <li><Link to="/help" className="hover:text-white transition-colors">Help center</Link></li>
              
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-6 sm:mt-8 pt-6 sm:pt-8 text-center text-gray-400 text-xs sm:text-sm">
          <p>&copy; 2025 DocSynecX. All rights reserved. Powered by SynecX AI Labs.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;