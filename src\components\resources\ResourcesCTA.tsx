import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";

const ResourcesCTA = () => {
  return (
    <section className="bg-gradient-to-r from-teal-600 to-green-600 py-20">
      <div className="container mx-auto px-6 text-center">
        <div className="max-w-3xl mx-auto text-white">
          <h2 className="text-4xl font-bold mb-6">
            Need Help with Integration?
          </h2>
          <p className="text-xl text-teal-100 mb-8">
            Our developer support team is here to help you implement DocSynecX APIs and SDKs
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg" asChild>
              <Link to="/contact">
                Join Developer Community
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg" asChild>
              <Link to="/contact">
                Schedule Technical Calls
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ResourcesCTA;
