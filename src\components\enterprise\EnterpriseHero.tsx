import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Workflow, <PERSON><PERSON>, <PERSON> } from "lucide-react";

const EnterpriseHero = () => {
  return (
    <section className="py-20 px-6">
      <div className="container mx-auto text-center">
        <div className="flex justify-center mb-6">
          <div className="flex items-center space-x-2 bg-gradient-to-r from-teal-100 to-green-100 text-teal-700 px-4 py-2 rounded-full text-sm">
            <Workflow className="w-4 h-4" />
            <span>Enterprise Document AI Platform</span>
          </div>
        </div>
        
        <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
          Enterprise Document AI Platform<br />
          <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">to Build Workflows</span>
        </h1>
        
        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          Transform your enterprise operations with intelligent document processing workflows. 
          Automate complex business processes, extract insights, and scale your document operations 
          with enterprise-grade security and compliance.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="group bg-gradient-to-r from-teal-600 to-green-600 text-white hover:from-teal-700 hover:to-green-700" onClick={() => {
            const form = document.getElementById('demo-request-form');
            if (form) form.scrollIntoView({ behavior: 'smooth' });
          }}>
            Schedule Enterprise Demo
            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>

          
          <Button variant="outline" size="lg" className="border-teal-600 text-teal-700 hover:bg-teal-50 hover:text-teal-900"onClick={() => {
            const form = document.getElementById('enterprise-features');
            if (form) form.scrollIntoView({ behavior: 'smooth' });
          }}>
            View Enterprise Features
          </Button>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 mt-16 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Bot className="w-6 h-6 text-teal-600" />
            </div>
            <h3 className="font-semibold text-lg mb-2">AI-Powered Workflows</h3>
            <p className="text-gray-600">Intelligent automation that learns from your business processes</p>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Shield className="w-6 h-6 text-green-700" />
            </div>
            <h3 className="font-semibold text-lg mb-2">Enterprise Security</h3>
            <p className="text-gray-600">SOC 2 Type II, end-to-end encryption</p>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-teal-100 to-green-50 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Workflow className="w-6 h-6 text-teal-600" />
            </div>
            <h3 className="font-semibold text-lg mb-2">Custom Workflows</h3>
            <p className="text-gray-600">Build tailored document processing workflows for your needs</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EnterpriseHero;