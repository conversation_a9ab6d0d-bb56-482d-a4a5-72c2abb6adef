import { ArrowLeft, Home, Search, FileText } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100">
      <Navbar />

      {/* 404 Content */}
      <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20">
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8 sm:mb-12">
            <h1 className="text-6xl sm:text-8xl font-bold text-gray-900 mb-4">404</h1>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4 sm:mb-6">
              Page Not Found
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 mb-8 sm:mb-12">
              The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-8 sm:mb-12">
            <Link to="/">
              <Button size="lg" className="bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg">
                <Home className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                Go Home
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg">
              <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
              Go Back
            </Button>
          </div>

          <div className="bg-white rounded-lg p-6 sm:p-8 shadow-lg">
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">
              Looking for something specific?
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <Link to="/platform" className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:border-teal-300 hover:bg-teal-50 transition-colors group">
                <FileText className="w-5 h-5 text-teal-600 mr-2" />
                <span className="text-gray-700 group-hover:text-teal-600">Platform</span>
              </Link>
              <Link to="/blog" className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:border-teal-300 hover:bg-teal-50 transition-colors group">
                <Search className="w-5 h-5 text-teal-600 mr-2" />
                <span className="text-gray-700 group-hover:text-teal-600">Blog</span>
              </Link>
              <Link to="/resources" className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:border-teal-300 hover:bg-teal-50 transition-colors group">
                <FileText className="w-5 h-5 text-teal-600 mr-2" />
                <span className="text-gray-700 group-hover:text-teal-600">Resources</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default NotFound;
