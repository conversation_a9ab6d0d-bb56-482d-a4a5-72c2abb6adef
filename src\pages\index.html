<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Document Preview - 3 Cards</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center p-8">

  <div class="flex flex-wrap justify-center gap-8">

    <!-- Card 1: Document Information -->
    <div class="relative w-full max-w-md">
      <div class="bg-white rounded-2xl shadow-2xl p-8 transform hover:scale-105 transition-all duration-500">
        <div class="flex items-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
               stroke-width="1.5" stroke="currentColor"
               class="w-8 h-8 text-teal-600 mr-3">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m-1.5 0h7.5m-7.5 3H12m-1.5 12V6.375c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v13.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 017.5 19.875v-1.5"/>
          </svg>
          <h3 class="text-xl font-bold text-gray-900">Document Information</h3>
        </div>
        <div class="space-y-4">
          <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600">Document Type:</span>
            <span class="font-semibold text-gray-900">Bill of Lading</span>
          </div>
          <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600">Document Number:</span>
            <span class="font-semibold text-gray-900">SSOF090406718</span>
          </div>
          <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600">Date Issued:</span>
            <span class="font-semibold text-gray-900">15 – August – 2010</span>
          </div>
          <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600">Currency:</span>
            <span class="font-semibold text-gray-900">USD</span>
          </div>
          <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600">Incoterm:</span>
            <span class="font-semibold text-gray-900">FOB (2010)</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-center">
          <div class="flex items-center text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                 viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="w-5 h-5 mr-2">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="font-medium">Data Extracted Successfully</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Card 2: Exporter Details -->
    <div class="relative w-full max-w-md">
      <div class="bg-white rounded-2xl shadow-2xl p-8 transform hover:scale-105 transition-all duration-500">
        <div class="flex items-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none"
               viewBox="0 0 24 24" stroke-width="1.5"
               stroke="currentColor" class="w-8 h-8 text-blue-600 mr-3">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18m2.25-18v18M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.75m-.75 3h.75m-.75 3h.75m-3.75-16.5h3.75A2.25 2.25 0 0121 6v12a2.25 2.25 0 01-2.25 2.25h-3.75m-16.5-3.75h16.5"/>
          </svg>
          <h3 class="text-xl font-bold text-gray-900">Exporter Details</h3>
        </div>
        <div class="space-y-4">
          <div class="flex flex-col p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 text-sm">Company Name:</span>
            <span class="font-semibold text-gray-900">Shenzhen Ailisheng Trade Co., Ltd.</span>
          </div>
          <div class="flex flex-col p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 text-sm">Address:</span>
            <span class="font-semibold text-gray-900 text-sm">Phoenix Road, Luohu district, Guangdong, Shenzhen city, China</span>
          </div>
          <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600">Telephone:</span>
            <span class="font-semibold text-gray-900">086-755-36922075</span>
          </div>
          <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600">Fax:</span>
            <span class="font-semibold text-gray-900">086-755-36922075</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-center">
          <div class="flex items-center text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                 viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="w-5 h-5 mr-2">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="font-medium">Data Extracted Successfully</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Card 3: Importer Details -->
    <div class="relative w-full max-w-md">
      <div class="bg-white rounded-2xl shadow-2xl p-8 transform hover:scale-105 transition-all duration-500">
        <div class="flex items-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none"
               viewBox="0 0 24 24" stroke-width="1.5"
               stroke="currentColor" class="w-8 h-8 text-purple-600 mr-3">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M13.5 21v-7.5a.75.75 0 01.75-.75h3a.75.75 0 01.75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.75m-16.5-7.5h16.5c.621 0 1.125-.504 1.125-1.125v-5.25c0-.621-.504-1.125-1.125-1.125h-16.5c-.621 0-1.125.504-1.125 1.125v5.25c0 .621.504 1.125 1.125 1.125z"/>
          </svg>
          <h3 class="text-xl font-bold text-gray-900">Importer Details</h3>
        </div>
        <div class="space-y-4">
          <div class="flex flex-col p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 text-sm">Company Name:</span>
            <span class="font-semibold text-gray-900">Alejead Pc S.A.S.</span>
          </div>
          <div class="flex flex-col p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 text-sm">Address:</span>
            <span class="font-semibold text-gray-900 text-sm">Aptdo Postal 28059, Carrera 100 5-39, Cali - Valle - Colombia</span>
          </div>
          <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600">Telephone:</span>
            <span class="font-semibold text-gray-900">059-032-4491451</span>
          </div>
          <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600">Email:</span>
            <span class="font-semibold text-gray-900 text-sm"><EMAIL></span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-center">
          <div class="flex items-center text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                 viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="w-5 h-5 mr-2">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="font-medium">Data Extracted Successfully</span>
          </div>
        </div>
      </div>
    </div>

  </div>

</body>
</html>
