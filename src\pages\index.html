<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Document Preview - 3 Cards</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen flex flex-col items-center justify-center p-8">

  <!-- Export Button -->
  <div class="mb-8">
    <button id="exportBtn" class="bg-gradient-to-r from-teal-600 to-blue-600 hover:from-teal-700 hover:to-blue-700 text-white font-bold py-3 px-8 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 flex items-center space-x-2">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
      </svg>
      <span>Export All Cards as Images</span>
    </button>
  </div>

  <div id="cardsContainer" class="flex flex-wrap justify-center gap-8 items-stretch">

    <!-- Card 1: Document Information -->
    <div id="card1" class="relative w-full max-w-xl h-full">
      <div class="bg-white rounded-2xl shadow-2xl p-8 transform hover:scale-105 transition-all duration-500 h-full flex flex-col">
        <div class="flex items-center mb-6">
          <i class="fas fa-file-alt text-3xl text-teal-600 mr-3"></i>
          <h3 class="text-xl font-bold text-gray-900">Document Information</h3>
        </div>
        <div class="space-y-4 flex-grow">
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Document Type:</span>
            <span class="font-semibold text-gray-900 text-right break-words">Bill of Lading</span>
          </div>
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Document Number:</span>
            <span class="font-semibold text-gray-900 text-right break-words">SSOF090406718</span>
          </div>
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Date Issued:</span>
            <span class="font-semibold text-gray-900 text-right break-words">15 – August – 2010</span>
          </div>
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Currency:</span>
            <span class="font-semibold text-gray-900 text-right break-words">USD</span>
          </div>
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Incoterm:</span>
            <span class="font-semibold text-gray-900 text-right break-words">FOB (2010)</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-center">
          <div class="flex items-center text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                 viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="w-5 h-5 mr-2">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="font-medium">Data Extracted Successfully</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Card 2: Exporter Details -->
    <div class="relative w-full max-w-xl h-full">
      <div class="bg-white rounded-2xl shadow-2xl p-8 transform hover:scale-105 transition-all duration-500 h-full flex flex-col">
        <div class="flex items-center mb-6">
          <i class="fas fa-file-alt text-3xl text-blue-600 mr-3"></i>
          <h3 class="text-xl font-bold text-gray-900">Exporter Details</h3>
        </div>
        <div class="space-y-4 flex-grow">
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Company Name:</span>
            <span class="font-semibold text-gray-900 text-right break-words">Shenzhen Ailisheng Trade Co., Ltd.</span>
          </div>
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Address:</span>
            <span class="font-semibold text-gray-900 text-sm text-right break-words">Phoenix Road, Luohu district, Guangdong, Shenzhen city, China</span>
          </div>
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Telephone:</span>
            <span class="font-semibold text-gray-900 text-right break-words">086-755-36922075</span>
          </div>
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Fax:</span>
            <span class="font-semibold text-gray-900 text-right break-words">086-755-36922075</span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-center">
          <div class="flex items-center text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                 viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="w-5 h-5 mr-2">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="font-medium">Data Extracted Successfully</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Card 3: Importer Details -->
    <div class="relative w-full max-w-xl h-full">
      <div class="bg-white rounded-2xl shadow-2xl p-8 transform hover:scale-105 transition-all duration-500 h-full flex flex-col">
        <div class="flex items-center mb-6">
          <i class="fas fa-file-alt text-3xl text-purple-600 mr-3"></i>
          <h3 class="text-xl font-bold text-gray-900">Importer Details</h3>
        </div>
        <div class="space-y-4 flex-grow">
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Company Name:</span>
            <span class="font-semibold text-gray-900 text-right break-words">Alejead Pc S.A.S.</span>
          </div>
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Address:</span>
            <span class="font-semibold text-gray-900 text-sm text-right break-words">Aptdo Postal 28059, Carrera 100 5-39, Cali - Valle - Colombia</span>
          </div>
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Telephone:</span>
            <span class="font-semibold text-gray-900 text-right break-words">059-032-4491451</span>
          </div>
          <div class="flex justify-between items-start p-3 bg-gray-50 rounded-lg animate-pulse">
            <span class="text-gray-600 flex-shrink-0 mr-3">Email:</span>
            <span class="font-semibold text-gray-900 text-sm text-right break-words"><EMAIL></span>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-center">
          <div class="flex items-center text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                 viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="w-5 h-5 mr-2">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="font-medium">Data Extracted Successfully</span>
          </div>
        </div>
      </div>
    </div>

  </div>

</body>
</html>
