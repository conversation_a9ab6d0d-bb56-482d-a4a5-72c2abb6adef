import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useEffect } from "react";


const PrivacyPolicy = () => (
  useEffect(() => {
    document.title = "Privacy Policy - DocSynecX Document Intelligence Platform";
  }, []),
  
  <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100 flex flex-col">
    <Navbar />
    <main className="flex-1 w-full bg-white flex items-center justify-center py-12 sm:py-16 lg:py-20">
      <div className="legal-page max-w-5xl w-full px-4 sm:px-8">
        <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-8 text-center">Privacy Policy</h1>
        <div className="space-y-8 text-gray-800 text-base text-left">
          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">The Short Version</h2>
          <p>Your data belongs to you—not us. We do not resell user data or information extracted from uploaded documents. We do, however, use this data to improve our services. Data security is our top priority, and we employ advanced measures to ensure your information is protected.</p>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">The Long Version</h2>
          <p>Docsynecx ("Docsynecx", "we", or "Service Provider") is committed to protecting the Personal Data of its Clients. This Privacy Policy is part of the Terms of Service. Any term defined in the Terms of Service applies here.</p>
          <p>By using or registering with Docsynecx through <a href="https://docsynecx.com" className="text-teal-600 underline">https://docsynecx.com</a> or any of its subdomains, the Client agrees to comply with this Privacy Policy. We may update this policy from time to time without prior notice. Updates will be published on our website or within our application.</p>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">1. Collected Data</h2>
          <p>When registering or using Docsynecx, we may voluntarily collect Personal Data for service performance. This may include:</p>
          <ul className="list-disc pl-6 space-y-1">
            <li>Full name</li>
            <li>Email address</li>
            <li>Phone number</li>
            <li>Company name</li>
            <li>Billing address</li>
            <li>Tax identification (e.g., GST)</li>
          </ul>
          <p>We may also collect behavioral and preference data through website or application interactions (pages visited, surveys answered, etc.).</p>
          <p>When using Docsynecx, users typically upload documents (PDFs, scans, etc.) via the app, email, API, or third-party integrations. These documents (Imported Documents) are processed to extract structured information (Parsed Content).</p>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">2. Usage of Data</h2>
          <p>We collect data to improve user experience and service functionality. Data is retained as necessary unless the user requests deletion via <a href="mailto:<EMAIL>" className="text-teal-600 underline"><EMAIL></a>. Upon such request, data will be deleted within 30 days with confirmation.</p>
          <p>We do not sell Personal Data, Imported Documents, or Parsed Content. Documents may be retained for AI training and product development and will be deleted post-use. This processing is based on legitimate interest under applicable data protection laws.</p>
          <p><b>Sensitive data</b> (e.g., racial, political, religious, health-related) must not be uploaded or processed through Docsynecx.</p>
          <ul className="list-disc pl-6 space-y-1">
            <li><b>Roles:</b> Data Controller: Personal Data, Data Processor: Imported Documents and Parsed Content</li>
          </ul>
          <p>Data may be used for:</p>
          <ul className="list-disc pl-6 space-y-1">
            <li>Internal records</li>
            <li>Product/service improvement</li>
            <li>Promotional communications (with consent)</li>
            <li>Automated decisions or profiling to improve experience</li>
          </ul>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">3. Third-Party Services</h2>
          <p>We may use external service providers for operations (e.g., analytics, hosting). These third parties may access limited Personal Data solely for operational purposes, not for marketing, unless otherwise specified or consented to. Clients may request integrations with third-party services (e.g., Google Sheets, Salesforce, Zapier). Responsibility for data transmission lies with the Client. If required by law or court order, Docsynecx may disclose Personal Data to authorities.</p>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">4. External Links</h2>
          <p>Docsynecx may link to third-party websites. We are not responsible for their content or privacy practices. Users should read those sites privacy policies before use.</p>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">5. User Data Control</h2>
          <ul className="list-disc pl-6 space-y-1">
            <li>Opt out of marketing</li>
            <li>Request a copy of their data</li>
            <li>Withdraw consent at any time</li>
            <li>Request complete data deletion</li>
          </ul>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">6. Security</h2>
          <p>We use industry-standard SSL encryption and secure infrastructure (Amazon Web Services, Google Cloud). Backups are created daily, and only authorized employees can access data. Passwords are stored as encrypted hash keys, not visible to staff. Clients are responsible for maintaining password confidentiality. Transmission of data via the internet carries inherent risks; we take extensive measures but cannot guarantee absolute security.</p>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">7. Cookies</h2>
          <p>We use cookies for performance, personalization, and advertising. Consent is required upon first visit. Disabling cookies may impact service functionality.</p>
          <p>Types of Cookies:</p>
          <ul className="list-disc pl-6 space-y-1">
            <li>Strictly Necessary (e.g., login authentication)</li>
            <li>Functionality (e.g., language preferences)</li>
            <li>Analytics (e.g., page visits, load speed)</li>
            <li>Advertising (e.g., ad impressions, retargeting)</li>
          </ul>
          <p>Tools used: Cloudflare, Google Analytics, Hotjar, Facebook Pixel, LinkedIn Pixel, Mixpanel, HubSpot, Apollo, Chameleon.</p>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">8. Communications</h2>
          <p>Docsynecx may contact users for operational or promotional reasons. Promotional emails will relate to the user's professional activity.</p>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">9. General Terms</h2>
          <ul className="list-disc pl-6 space-y-1">
            <li>Rights under this policy cannot be transferred without consent.</li>
            <li>Invalid provisions will not affect the remainder of the policy.</li>
            <li>Delay or inaction does not imply waiver of any right.</li>
            <li>Goverened by the laws of India. Disputes will be resolved under the jurisdiction of Indian courts.</li>
          </ul>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">10. Business Changes</h2>
          <p>In case of acquisition, merger, or sale, user data may be transferred to new owners under the terms of this policy. We will take steps to ensure privacy is protected.</p>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">11. Policy Changes</h2>
          <p>Docsynecx may amend this Privacy Policy as required by law or operational necessity. Updates will be posted on our website. Continued use of the platform implies acceptance of the updated terms.</p>

          <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Contact</h2>
          <p>For concerns or questions about your data, contact: <a href="mailto:<EMAIL>" className="text-teal-600 underline"><EMAIL></a></p>
        </div>
      </div>
    </main>
    <Footer />
  </div>
);

export default PrivacyPolicy; 