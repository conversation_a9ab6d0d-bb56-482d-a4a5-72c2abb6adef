import { Card, CardContent } from "@/components/ui/card";
import { Upload, Brain, CheckCircle, Workflow } from "lucide-react";

const DocumentProcessingProcess = () => {
  const steps = [
    {
      icon: Upload,
      title: "Document Ingestion",
      description: "Upload documents via API, email, or web interface. Support for 100+ file formats including PDFs, images, and scanned documents.",
      features: ["Batch processing", "Real-time upload", "Cloud storage integration"]
    },
    {
      icon: Brain,
      title: "AI-Powered Analysis",
      description: "Advanced OCR and NLP extract structured data, classify document types, and understand context with 97.0% accuracy.",
      features: ["Text extraction", "Image recognition", "Data validation"]
    },
    {
      icon: CheckCircle,
      title: "Quality Assurance",
      description: "Automated validation checks ensure data accuracy with confidence scoring and human-in-the-loop verification when needed.",
      features: ["Confidence scoring", "Error detection", "Manual review queue"]
    },
    {
      icon: Workflow,
      title: "System Integration",
      description: "Seamlessly integrate extracted data into your existing systems via APIs, webhooks, or direct database connections.",
      features: ["API endpoints", "Webhook notifications", "Database sync"]
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            How It <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">Works</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our intelligent document processing pipeline transforms unstructured documents 
            into structured, actionable data in four simple steps.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {steps.map((step, index) => (
            <Card key={index} className="relative">
              <CardContent className="p-8">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {index + 1}
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center mb-4">
                      <step.icon className="h-6 w-6 text-green-600 mr-3" />
                      <h3 className="text-xl font-semibold text-gray-900">{step.title}</h3>
                    </div>
                    <p className="text-gray-600 mb-4">{step.description}</p>
                    <ul className="space-y-2">
                      {step.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                          <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="bg-white rounded-2xl p-8 shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-green-600 mb-2">10x</div>
              <div className="text-gray-600">Faster Processing</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600 mb-2">97.0%</div>
              <div className="text-gray-600">Accuracy Rate</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600 mb-2">50+</div>
              <div className="text-gray-600">Document Types</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DocumentProcessingProcess;