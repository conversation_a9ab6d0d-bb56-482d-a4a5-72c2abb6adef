import { useState, useEffect } from "react";
import { ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { initEmailJS, sendDemoEmail } from "@/lib/emailjs";

const ResourcesDemoForm = () => {
  const [demoForm, setDemoForm] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Initialize EmailJS when component mounts
  useEffect(() => {
    initEmailJS();
  }, []);

  const handleDemoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const result = await sendDemoEmail(demoForm);

      if (result.success) {
        toast({
          title: "Demo Request Submitted Successfully! 🎉",
          description: "Thank you for your interest! Our team will contact you within 24 hours to schedule your personalized demo.",
        });
        setDemoForm({ name: '', email: '', company: '', message: '' });
      } else {
        toast({
          title: "Submission Failed",
          description: "There was a problem submitting your request. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Network Error",
        description: "Could not send your request. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Request a Personalized Demo
          </h2>
          <p className="text-xl text-gray-600">
            See DocSynecX in action with a customized demonstration tailored to your use case
          </p>
        </div>
        
        <Card className="shadow-xl">
          <CardHeader>
            <CardTitle className="text-2xl text-center">Schedule Your Demo</CardTitle>
            <CardDescription className="text-center text-base">
              Our experts will show you how DocSynecX can transform your document workflows
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleDemoSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    value={demoForm.name}
                    onChange={(e) => setDemoForm({...demoForm, name: e.target.value})}
                    required
                    placeholder="Enter your full name"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={demoForm.email}
                    onChange={(e) => setDemoForm({...demoForm, email: e.target.value})}
                    required
                    placeholder="Enter your email"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="company">Company Name *</Label>
                <Input
                  id="company"
                  value={demoForm.company}
                  onChange={(e) => setDemoForm({...demoForm, company: e.target.value})}
                  required
                  placeholder="Enter your company name"
                />
              </div>
              <div>
                <Label htmlFor="message">Tell us about your use case</Label>
                <Textarea
                  id="message"
                  value={demoForm.message}
                  onChange={(e) => setDemoForm({...demoForm, message: e.target.value})}
                  placeholder="Describe your document processing needs, current challenges, or specific features you'd like to see"
                  rows={4}
                />
              </div>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 text-lg py-3 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Sending...' : 'Request Demo'}
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default ResourcesDemoForm;
