import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, Scan, Database, Download, FileCheck, Zap } from "lucide-react";

const LogisticsTradeExtraction = () => {
  const extractionSteps = [
    {
      icon: Upload,
      title: "Document Upload",
      description: "Upload documents via API, web interface, or email integration"
    },
    {
      icon: Scan,
      title: "AI Analysis",
      description: "Advanced OCR and machine learning models process the document"
    },
    {
      icon: Database,
      title: "Data Extraction",
      description: "Extract structured data with field-level confidence scores"
    },
    {
      icon: Download,
      title: "Export & Integration",
      description: "Export to ERP, TMS, or WMS systems via API or file export"
    }
  ];

  const features = [
    {
      icon: FileCheck,
      title: "Multi-format Support",
      description: "Process PDF, images, scanned documents, and digital files",
      benefit: "95% faster processing"
    },
    {
      icon: Zap,
      title: "Real-time Processing",
      description: "Get extracted data within seconds of document upload",
      benefit: "3-5 second processing"
    },
    {
      icon: Database,
      title: "Smart Validation",
      description: "Cross-reference data with master databases and compliance rules",
      benefit: "99.9% data accuracy"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-green-50 to-teal-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Document Injection & Data Extraction
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Seamlessly inject documents into our AI platform and extract structured data with unmatched precision
          </p>
        </div>

        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">How It Works</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {extractionSteps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-teal-100 to-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <step.icon className="w-8 h-8 text-teal-600" />
                </div>
                <div className="mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-teal-600 to-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-2 text-sm font-bold">
                    {index + 1}
                  </div>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h4>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {features.map((feature, index) => (
            <Card key={index} className="border border-green-100 bg-white shadow text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-br from-teal-50 to-green-50 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="w-6 h-6 text-teal-600" />
                </div>
                <CardTitle className="text-xl text-gray-900">{feature.title}</CardTitle>
                <CardDescription className="text-gray-600">{feature.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <p className="text-green-800 font-semibold text-sm">{feature.benefit}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* <div className="text-center">
          <Button size="lg" className="mr-4 bg-gradient-to-r from-teal-600 to-green-600 text-white hover:from-teal-700 hover:to-green-700">
            Try Document Injection
          </Button>
          <Button variant="outline" size="lg" className="border-teal-600 text-teal-700 hover:bg-teal-50 hover:text-teal-900">
            View API Docs
          </Button>
        </div> */}
      </div>
    </section>
  );
};

export default LogisticsTradeExtraction;