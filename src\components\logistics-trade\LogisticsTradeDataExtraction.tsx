import { useState } from "react";
import InteractiveKeyValueList from "@/components/InteractiveKeyValueList";

const LogisticsTradeDataExtraction = () => {
  const [showJson, setShowJson] = useState(false);
  const [selectedField, setSelectedField] = useState<string | null>(null);

const logisticsData = {
  "Document Information": {
    "Document Type": "Bill of Lading",
    "Document Number": "SSOF090406718",
    "Date Issued": "15 – August – 2010",
    "Currency": "USD",
    "Incoterm": "FOB (2010)"
  },
  "Exporter Details": {
    "Company Name": "Shenzhen Ailisheng Trade Co., Ltd.",
    "Address": "Phoenix Road, Luohu district, Guangdong, Shenzhen city, China",
    "Telephone": "086-755-36922075",
    "Fax": "086-755-36922075"
  },
  "Importer Details": {
    "Company Name": "Alejead Pc S.A.S.",
    "Address": "Aptdo Postal 28059, Carrera 100 5-39, Cali - Valle - Colombia",
    "Telephone": "059-032-4491451",
    "Email": "<EMAIL>"
  },
  "Notify Party": {
    "Company Name": "Same as consignee"
  },
  "Shipment Details": {
    "Packing List No": "*********-1",
    "Port of Loading": "Shangai / China",
    "Place of Receipt": "Shen Zhen / China",
    "Port of Discharge": "Buenaventura / Colombia",
    "Place of Delivery": "Cali / Colombia",
    "Vessel Name": "CSCL LE HAVRE",
    "Voyage Number": "0029W",
    "Transshipment": "To: Maersk Line",
    "Container Number": "CSQU3054383",
    "Container Type": "20’ steel Dry Cargo Container",
    "Total Containers/Packages Received by Carrier": "1 / 0",
    "Marks and Numbers": "CSQU3054383"
  },
  "Goods Description": {
    "Number of Packages": "500 packages",
    "Description": "500 units of 15.6 inch laptop with core i7 8GB RAM, in 6 pallets with 80 packages each one with a volume of 1.63 M³ and 1 pallet with 20 packages with a volume of 0.41 M³",
    "Gross Weight": "1650 Kg",
    "Measurement": "10.2 M³"
  },
  "Financial Details": {
    "Ocean Freight": "USD 3.300",
    "Freight and Charges Payable By": "Shipper at Shen Zhen / Guangdong",
    "Freight Payment Status": "Prepaid"
  },
  "Other Details": {
    "Number of Original B/L Issued": "Three (3)",
    "Shipped on Board Date": "20 – August – 2010",
    "Place of Issue": "Shen Zhen / China",
    "Signature": "------------------------------",
    "Release Contact": {
      "Company": "Agencia de Aduanas Siacomex Ltda – Buenaventura",
      "Address": "Calle 2 No. 2ª-58",
      "PBX": "(052) 242 2798",
      "Fax": "(052) 242 4823",
      "Email": "<EMAIL>"
    },
    "Legal Notes": "The contract evidenced by Bill of Landing is governed by the laws of the Hong Kong Special Administrative Region. Any proceeding against the carrier must be brought in the courts of the Hong Kong Special Administrative Region and no other court."
  }
};



  return (
    <section className="bg-gradient-to-br from-slate-50 via-teal-50 to-green-100 py-16 lg:py-20">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            See Logistics Document Data Extraction in Action
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            See how DocSynecX extracts data from trade documents in real-time
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 max-w-6xl mx-auto">
          {/* Left: Document Image */}
          <div className="bg-white rounded-2xl shadow-lg p-4 min-h-[600px] max-h-[600px] relative overflow-hidden">
            <div className="w-full h-full relative">
              <img 
                src="/bill-of-landing.jpg" 
                alt="Sample Bill of Lading Document" 
                className="w-full h-full object-cover rounded-lg shadow-sm border border-gray-200"
              />
            </div>
          </div>

          {/* Right: Extracted Data */}
          <div className="bg-white rounded-2xl shadow-lg p-0 min-h-[600px] max-h-[600px] flex flex-col relative">
            {/* Toggle Buttons */}
            <div className="absolute top-4 right-4 z-20 flex gap-2">
              <button
                className={`px-3 py-1 text-sm font-medium rounded shadow border transition ${
                  !showJson 
                    ? 'bg-teal-600 text-white border-teal-600' 
                    : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'
                }`}
                onClick={() => setShowJson(false)}
              >
                List View
              </button>
              <button
                className={`px-3 py-1 text-sm font-medium rounded shadow border transition ${
                  showJson 
                    ? 'bg-teal-600 text-white border-teal-600' 
                    : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'
                }`}
                onClick={() => setShowJson(true)}
              >
                JSON View
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-8">
              {showJson ? (
                <div>
                  <div className="mb-4 font-bold text-lg text-gray-700">JSON Data</div>
                  <pre className="bg-white text-green-600 rounded-lg p-4 text-xs sm:text-sm overflow-x-auto max-h-[60vh] whitespace-pre-wrap border border-gray-200">
                    {JSON.stringify(logisticsData, null, 2)}
                  </pre>
                </div>
              ) : (
                <>
                  <InteractiveKeyValueList
                    data={logisticsData["Document Information"]}
                    heading="Document Information"
                    selectedField={selectedField}
                    onFieldClick={setSelectedField}
                  />
                  <InteractiveKeyValueList
                    data={logisticsData["Exporter Details"]}
                    heading="Exporter Details"
                    selectedField={selectedField}
                    onFieldClick={setSelectedField}
                  />
                  <InteractiveKeyValueList
                    data={logisticsData["Importer Details"]}
                    heading="Importer Details"
                    selectedField={selectedField}
                    onFieldClick={setSelectedField}
                  />
                  <InteractiveKeyValueList
                    data={logisticsData["Shipment Details"]}
                    heading="Shipment Details"
                    selectedField={selectedField}
                    onFieldClick={setSelectedField}
                  />
                  <InteractiveKeyValueList
                    data={logisticsData["Goods Description"]}
                    heading="Goods Description"
                    selectedField={selectedField}
                    onFieldClick={setSelectedField}
                  />
                  <InteractiveKeyValueList
                    data={logisticsData["Financial Details"]}
                    heading="Financial Details"
                    selectedField={selectedField}
                    onFieldClick={setSelectedField}
                  />
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LogisticsTradeDataExtraction;
