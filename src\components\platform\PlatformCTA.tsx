import { <PERSON><PERSON><PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const PlatformCTA = () => {
  return (
    <section className="bg-gradient-to-r from-teal-600 to-green-600 py-20">
      <div className="container mx-auto px-6 text-center">
        <div className="max-w-3xl mx-auto text-white">
          <h2 className="text-4xl font-bold mb-6">
            Ready to Transform Your Document Workflows?
          </h2>
          <p className="text-xl text-teal-100 mb-8">
            Get started with DocSynecX today and experience the power of AI-driven document processing
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg" asChild>
              <a href="https://app.docsynecx.com/signin/" target="_blank" rel="noopener noreferrer">
                Start Free Trial
                <ArrowRight className="ml-2 w-5 h-5" />
              </a>
            </Button>
            <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg" asChild>
              <Link to="/contact">
                Contact Sales
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PlatformCTA;
