import { ArrowRight, FileText, Users, Target, Award, Globe, Shield, Zap, Database, Code, TrendingUp, Heart } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import Footer from "@/components/Footer";

const About = () => {

  // Set page title when component mounts
  useEffect(() => {
    document.title = "Intelligence Document Processing Platform - DocSynecX";
  }, []);

  const milestones = [
    {
      year: "2020",
      title: "Company Founded",
      description: "DocSynecX established with vision to revolutionize document processing through AI"
    },
    {
      year: "2021",
      title: "First OCR AI Model",
      description: "Launched proprietary OCR engine achieving 97.0% accuracy on invoice processing"
    },
    {
      year: "2022",
      title: "Enterprise Integration",
      description: "Expanded to support SAP, Tally, Oracle and 50+ ERP system integrations"
    },
    {
      year: "2023",
      title: "Global Expansion",
      description: "Reached 10M+ documents processed monthly across 25+ countries"
    },
    {
      year: "2024",
      title: "AI Platform 2.0",
      description: "Next-generation document AI platform with advanced parsing and real-time processing"
    }
  ];

  const values = [
    {
      icon: Target,
      title: "Innovation First",
      description: "Continuously pushing boundaries in OCR AI and document processing technologies"
    },
    {
      icon: Shield,
      title: "Security & Compliance",
      description: "Enterprise-grade security with SOC 2, and industry compliance standards"
    },
    {
      icon: Users,
      title: "Customer Success",
      description: "Dedicated to delivering measurable ROI and seamless integration experiences"
    },
    {
      icon: Globe,
      title: "Global Impact",
      description: "Empowering businesses worldwide to digitize and automate document workflows"
    }
  ];

  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100">
      {/* Navigation */}
      <Navbar />

      {/* Hero Section */}
      <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20 text-center relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-teal-50/50 via-transparent to-green-50/50"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-teal-100 rounded-full blur-3xl opacity-30"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-green-100 rounded-full blur-3xl opacity-30"></div>
        
        <div className="max-w-4xl mx-auto relative z-10">
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-teal-100 to-green-100 text-teal-800 text-sm font-medium rounded-full mb-6 border border-teal-200">
            <FileText className="w-4 h-4 mr-2" />
            Intelligent Document Processing Platform
          </div>
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight px-4">
            About -{" "}
            <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
              DocSynecX IDP Platform
            </span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 mb-8 sm:mb-10 max-w-3xl mx-auto leading-relaxed px-4">
            DocSynecX is a cutting-edge Intelligent Document Processing (IDP) platform built to transform how businesses manage and automate document workflows at scale.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4">
            <Button size="lg" className="bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 px-8 py-4 text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
              <a href="https://app.docsynecx.com/signin/" target="_blank" rel="noopener noreferrer" className="flex items-center">
                Get Started Free
                <ArrowRight className="ml-2 w-5 h-5" />
              </a>
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="px-8 py-4 text-lg border-2 border-teal-600 text-teal-600 hover:bg-teal-50 transition-all duration-200"
              onClick={() => {
                const demoSection = document.getElementById('demo-request-section');
                if (demoSection) {
                  demoSection.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            >
              Request Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="bg-white py-12 sm:py-16 lg:py-20 relative">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-16 items-center mb-16 sm:mb-24">
              <div className="space-y-6">
                <div className="inline-flex items-center px-3 py-1 bg-teal-100 text-teal-800 text-sm font-medium rounded-full">
                  <FileText className="w-4 h-4 mr-2" />
                  About DocSynecX
                </div>
              
                <p className="text-base sm:text-lg text-gray-600 leading-relaxed">
                  Founded in 2024 by Synecx AI Labs, DocSynecX is an enterprise-grade Document AI platform that 
                  leverages machine learning and artificial intelligence to convert unstructured documents into 
                  structured, usable data — driving operational efficiency, compliance, and business intelligence 
                  across industries.
                </p>
                <div className="mb-6">
                  <h4 className="text-lg font-semibold mb-4 text-gray-900">Why Choose DocSynecX?</h4>
                  <div className="space-y-3">
                    {[
                      "AI-driven data capture from any document format",
                      "Built for enterprise scalability and industry-specific use cases",
                      "On-premise and cloud deployment options",
                      "Secure, compliant, and easy to integrate",
                      "Human-in-the-loop workflows for higher accuracy"
                    ].map((item, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="w-5 h-5 bg-teal-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <div className="w-2 h-2 bg-teal-600 rounded-full"></div>
                        </div>
                        <span className="text-sm text-gray-600 leading-relaxed">{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
              </div>
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-teal-600 to-green-600 rounded-2xl transform rotate-3 opacity-10"></div>
                <div className="bg-gradient-to-br from-teal-600 to-green-600 rounded-2xl p-8 text-white relative shadow-2xl">
                  <div className="absolute top-4 right-4 w-12 h-12 bg-white/10 rounded-full flex items-center justify-center">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-bold mb-4">Our Mission</h3>
                  <p className="text-teal-100 mb-6 leading-relaxed text-sm sm:text-base">
                    To simplify and automate complex document workflows through AI-powered intelligence — enabling 
                    organizations to focus on strategic work while we handle the data.
                  </p>
                  <div className="mb-6">
                    <div className="flex items-center mb-4">
                      <h3 className="text-xl sm:text-2xl font-bold">Our Vision</h3>
                    </div>
                    <p className="text-teal-100 leading-relaxed text-sm sm:text-base">
                      To become the global standard in document automation by making enterprise-grade IDP accessible, 
                      intelligent, and scalable for every business.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Core Values */}
            <div className="text-center mb-12 sm:mb-16">
              <div className="inline-flex items-center px-3 py-1 bg-teal-100 text-teal-800 text-sm font-medium rounded-full mb-6">
                <Heart className="w-4 h-4 mr-2" />
                Core Values
              </div>
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4 px-4">
                The Principles That Guide Us
              </h2>
              <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-4">
                Our core values shape everything we do at DocSynecX, from product development to customer success
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
              {values.map((value, index) => (
                <Card key={index} className="group text-center hover:shadow-2xl transition-all duration-300 border-0 shadow-lg hover:scale-105 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-6">
                    <div className="relative">
                      <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-green-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <value.icon className="w-8 h-8 text-white" />
                      </div>
                      <div className="absolute inset-0 bg-gradient-to-br from-teal-500 to-green-500 rounded-2xl opacity-20 blur-xl group-hover:opacity-30 transition-opacity duration-300"></div>
                    </div>
                    <CardTitle className="text-lg sm:text-xl font-bold text-gray-900 group-hover:text-teal-700 transition-colors duration-300">
                      {value.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pb-6">
                    <CardDescription className="text-sm sm:text-base leading-relaxed text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                      {value.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>


      {/* Demo Request Section */}
      <div id="demo-request-section">
        <DemoRequestForm />
      </div>


 {/* CTA Section */}
        <section className="bg-gradient-to-r from-teal-600 to-green-600 py-20">
          <div className="container mx-auto px-6 text-center">
            <div className="max-w-3xl mx-auto text-white">
              <h2 className="text-4xl font-bold mb-6">
                Ready to Automate Your Document Workflow with IDP?
              </h2>
              <p className="text-xl text-teal-100 mb-8">
                Experience the power of our IDP solution for seamless document processing, and intelligent data extraction.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg" asChild>
                  <a href="https://app.docsynecx.com/signin/" target="_blank" rel="noopener noreferrer">
                    Start Free Trial
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </a>
                </Button>
                <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg" asChild>
                  <Link to="/contact">
                    Talk to Experts
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      {/* Footer */}
      <Footer />
    </div>
  );
};

export default About;
