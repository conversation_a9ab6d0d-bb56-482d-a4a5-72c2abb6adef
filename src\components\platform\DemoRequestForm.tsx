import { useState, useEffect } from "react";
import { ArrowR<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { initEmailJS, sendDemoEmail } from "@/lib/emailjs";

const DemoRequestForm = () => {
  const [demoForm, setDemoForm] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Initialize EmailJS when component mounts
  useEffect(() => {
    initEmailJS();
  }, []);

  const handleDemoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const result = await sendDemoEmail(demoForm);

      if (result.success) {
        toast({
          title: "Demo Request Submitted Successfully! 🎉",
          description: "Thank you for your interest! Our team will contact you within 24 hours to schedule your personalized demo.",
        });
        setDemoForm({ name: '', email: '', phone: '', company: '', message: '' });
      } else {
        toast({
          title: "Submission Failed",
          description: "There was a problem submitting your request. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Network Error",
        description: "Could not send your request. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="demo-request-form" className="bg-white py-12 sm:py-16 lg:py-20">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
              See DocSynecX in Action
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 px-4">
              Schedule a personalized demo and discover how DocSynecX can transform your document workflows
            </p>
          </div>
          
          <Card className="shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl sm:text-2xl text-center">Request Your Demo</CardTitle>
              <CardDescription className="text-center text-sm sm:text-base">
                Get a customized demonstration tailored to your specific use case and requirements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleDemoSubmit} className="space-y-4 sm:space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name" className="text-sm sm:text-base">Full Name *</Label>
                    <Input
                      id="name"
                      value={demoForm.name}
                      onChange={(e) => setDemoForm({...demoForm, name: e.target.value})}
                      required
                      placeholder="Enter your full name"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-sm sm:text-base">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={demoForm.email}
                      onChange={(e) => setDemoForm({...demoForm, email: e.target.value})}
                      required
                      placeholder="Enter your email"
                      className="mt-1"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone" className="text-sm sm:text-base">Phone Number *</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={demoForm.phone}
                      onChange={(e) => setDemoForm({...demoForm, phone: e.target.value})}
                      required
                      placeholder="Enter your phone number"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="company" className="text-sm sm:text-base">Company Name *</Label>
                    <Input
                      id="company"
                      value={demoForm.company}
                      onChange={(e) => setDemoForm({...demoForm, company: e.target.value})}
                      required
                      placeholder="Enter your company name"
                      className="mt-1"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="message" className="text-sm sm:text-base">Tell us about your requirements</Label>
                  <Textarea
                    id="message"
                    value={demoForm.message}
                    onChange={(e) => setDemoForm({...demoForm, message: e.target.value})}
                    placeholder="Describe your document processing needs, current challenges, or specific features you'd like to see in the demo"
                    rows={4}
                    className="mt-1"
                  />
                </div>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 text-base sm:text-lg py-3 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Sending...' : 'Schedule Demo'}
                  <ArrowRight className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default DemoRequestForm;
