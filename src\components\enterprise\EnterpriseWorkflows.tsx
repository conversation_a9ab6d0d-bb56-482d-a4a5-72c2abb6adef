import { ArrowRight, CheckCircle, Clock, Users, FileCheck } from "lucide-react";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";

const EnterpriseWorkflows = () => {
  const workflowSteps = [
    {
      icon: FileCheck,
      title: "Document Ingestion",
      description: "Automatically capture documents from multiple sources - email, web portals, APIs, or direct uploads"
    },
    {
      icon: CheckCircle,
      title: "AI Processing",
      description: "Extract, classify, and validate data using advanced machine learning models trained on your document types"
    },
    {
      icon: Users,
      title: "Human Review",
      description: "Route documents requiring human oversight to the right team members with smart assignment rules"
    },
    {
      icon: Clock,
      title: "Workflow Automation",
      description: "Execute business logic, integrate with existing systems, and trigger downstream processes automatically"
    }
  ];

  const useCases = [
    {
      title: "Invoice Processing Automation",
      description: "Streamline accounts payable with automated invoice capture, data extraction, approval routing, and ERP integration.",
      benefits: ["97.0% accuracy", "75% faster processing", "Automated 3-way matching"]
    },
    {
      title: "Logistics & Supply Chain",
      description: "Streamline cross-border logistics with enterprise-grade document workflows for customs clearance, shipment tracking, and compliance.",
      benefits: ["Scalable for high volume operations", "Compiliance-Ready Automation", "Integrated with ERP and TMS"]
    },
    {
      title: "Customer Onboarding",
      description: "Accelerate customer acquisition with automated KYC document processing, identity verification, and compliance checks.",
      benefits: ["Reduced onboarding time", "Enhanced compliance", "Better customer experience"]
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-6">
        {/* How Document AI Platform Builds Workflows */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            How Document AI Platform Builds Workflows
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our platform transforms complex document processes into automated workflows that scale with your business
          </p>
        </div>

        {/* Workflow Steps */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {workflowSteps.map((step, index) => (
            <div key={index} className="relative">
              <Card className="text-center h-full">
                <CardHeader>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <step.icon className="w-6 h-6 text-green-600" />
                  </div>
                  <CardTitle className="text-lg">{step.title}</CardTitle>
                  <CardDescription className="text-sm">
                    {step.description}
                  </CardDescription>
                </CardHeader>
              </Card>
              {index < workflowSteps.length - 1 && (
                <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                  <ArrowRight className="w-6 h-6 text-green-400" />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Enterprise Use Cases */}
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            Enterprise Workflow Solutions
          </h3>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Pre-built workflows tailored for common enterprise use cases
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {useCases.map((useCase, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-xl mb-3">{useCase.title}</CardTitle>
                <CardDescription className="text-base mb-4">
                  {useCase.description}
                </CardDescription>
                <div className="space-y-2">
                  {useCase.benefits.map((benefit, benefitIndex) => (
                    <div key={benefitIndex} className="flex items-center text-sm text-green-700">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                      {benefit}
                    </div>
                  ))}
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default EnterpriseWorkflows;