import { ArrowRight, FileText, Building, Briefcase, GraduationCap, Heart, Scale, TrendingUp, CheckCircle, Shield } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Link, useLocation } from "react-router-dom";
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import Footer from "@/components/Footer";

const Solutions = () => {
  const location = useLocation();

  useEffect(() => {
    if (location.hash) {
      // Remove the # and get the element
      const el = document.getElementById(location.hash.replace("#", ""));
      if (el) {
        el.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    }
  }, [location]);

  useEffect(() => {
  document.title = "AI-Powered Document Intelligence Solutions - DocSynecX";
}, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100 flex flex-col">
      {/* Navigation */}
      <Navbar />
      {/* Main Content */}
      <main className="flex-1">
        {/* Hero Section */}
        <section className="container mx-auto px-6 py-20 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Next-Generation IDP Platform &{" "}
              <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
                AI-Powered Solutions
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Streamline your document workflows with Automated Document Intelligence and<br />
              AI-driven data extraction – delivering tailored solutions for every industry.
            </p>

          </div>
        </section>

        {/* Document Types with OCR Focus */}
        <section className="bg-white py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Document Types & Automated Data Processing
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Our AI-powered platform handles any document format with precision, from invoice processed data to complex parsed data extraction.
              </p>
            </div>

            <Tabs defaultValue="business" className="w-full">
              <TabsList className="grid w-full grid-cols-4 max-w-2xl mx-auto mb-12">
                <TabsTrigger value="business">Invoices</TabsTrigger>
                <TabsTrigger value="legal">Receipts</TabsTrigger>
                <TabsTrigger value="financial">Logistics</TabsTrigger>
                <TabsTrigger value="trade">Custom</TabsTrigger>
              </TabsList>

              <TabsContent value="business" className="space-y-8">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    "Tax Invoice", "GST Invoice", "Sales Invoice",
                    "Purchase Invoice", "Proforma Invoice", "Purchase Order",
                    "GST B2B", "GST B2C", "Custom Invoice",
                  ].map((docType) => (
                    <Card key={docType} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
                            <FileText className="w-4 h-4 text-teal-600" />
                          </div>
                          <CardTitle className="text-base">{docType}</CardTitle>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="legal" className="space-y-8">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    "POS Receipts", "POS Invoice", "Custom Receipts",
                  ].map((docType) => (
                    <Card key={docType} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
                            <Scale className="w-4 h-4 text-teal-600" />
                          </div>
                          <CardTitle className="text-base">{docType}</CardTitle>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="financial" className="space-y-8">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    "Bill of Lading", "e-Way Bill", "Proof of Delivery",
                    "Trade Invoice", "Carriers - DHL - UPS - USPS - FedEx",
                    "Custom",
                  ].map((docType) => (
                    <Card key={docType} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
                            <TrendingUp className="w-4 h-4 text-teal-600" />
                          </div>
                          <CardTitle className="text-base">{docType}</CardTitle>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="trade" className="space-y-8">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    "Custom Documents"
                  ].map((docType) => (
                    <Card key={docType} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
                            <Building className="w-4 h-4 text-teal-600" />
                          </div>
                          <CardTitle className="text-base">{docType}</CardTitle>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* OCR & Processing Features */}
        <section className="container mx-auto px-6 py-20">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Document AI Intelligence Processing Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Experience the power of AI-powered document intelligence and automated data extraction across all document types
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
            <Card className="group hover:shadow-xl transition-all duration-300 h-full flex flex-col">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <FileText className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl">Intelligent Document Processing</CardTitle>
                <CardDescription className="text-base">
                  Advanced AI-Powered Document reader with intelligent text extraction from any format.
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col flex-1">
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />AI-driven data extraction</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Real-time data parsing and document classification</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Seamless workflow automation</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300 h-full flex flex-col">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <TrendingUp className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl">Invoice Processing Automation</CardTitle>
                <CardDescription className="text-base">
                  Complete invoice processing solution - from processing an invoice to invoices processing at scale
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col flex-1">
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Process invoice automatically</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Invoice data extraction</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Processing of invoices workflow</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300 h-full flex flex-col">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <TrendingUp className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl">Logistics Solutions</CardTitle>
                <CardDescription className="text-base">
                  Streamline Supply Chain operations with intelligent Document Processing.
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col flex-1">
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Automated Bill of lading processing</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Cargo manifest automation</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Shipping document parsing</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300 h-full flex flex-col">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <FileText className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl">Receipt Processing</CardTitle>
                <CardDescription className="text-base">
                  Automate expense management with AI-Powered receipt processing and real-time expense tracking
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col flex-1">
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />AI-driven Receipt Capture</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Expense categorization</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Tax compliance automation</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300 h-full flex flex-col">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <Scale className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl">Custom Document Automation</CardTitle>
                <CardDescription className="text-base">
                  Tailored IDP solutions for industry-specific documents with custom field extraction and validation
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col flex-1">
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Custom template creation</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Field validation rules</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Workflow automation</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300 h-full flex flex-col">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <Building className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl">API Services</CardTitle>
                <CardDescription className="text-base">
                  API integration for seamless document automation.
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col flex-1">
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />API integration</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Scalable API endpoints</li>
                  <li className="flex items-center"><CheckCircle className="w-4 h-4 text-teal-500 mr-2" />Secure API access</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* By Industry Section */}
        <section id="by-industry" className="bg-white py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                By Industry
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Specialized Intelligent Document processing solutions tailored for your industry's unique requirements
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-teal-600">
                <CardHeader className="text-center py-8">
                  <CardTitle className="text-xl font-semibold text-gray-900">Banking & Finance</CardTitle>
                </CardHeader>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-teal-600">
                <CardHeader className="text-center py-8">
                  <CardTitle className="text-xl font-semibold text-gray-900">Insurance</CardTitle>
                </CardHeader>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-teal-600">
                <CardHeader className="text-center py-8">
                  <CardTitle className="text-xl font-semibold text-gray-900">HealthCare</CardTitle>
                </CardHeader>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-teal-600">
                <CardHeader className="text-center py-8">
                  <CardTitle className="text-xl font-semibold text-gray-900">Logistics</CardTitle>
                </CardHeader>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-teal-600">
                <CardHeader className="text-center py-8">
                  <CardTitle className="text-xl font-semibold text-gray-900">Commercial Real Estate</CardTitle>
                </CardHeader>
              </Card>

              {/* <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-teal-600">
                <CardHeader className="text-center py-8">
                  <CardTitle className="text-xl font-semibold text-gray-900">Human Resources & Employee Onboarding</CardTitle>
                </CardHeader>
              </Card> */}
            </div>
          </div>
        </section>

        {/* Use Cases with OCR Focus */}
        <section id="usecase" className="container mx-auto px-6 py-20">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Real-World Document AI Use Cases
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover how AI-Powered Document processing drives measurable results
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">Finance & Accounting</CardTitle>
                <CardDescription className="text-base">
                  Transform your invoicing workflow with AI-Powered IDP and reduce processing time by over 90%
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Before:</strong> 45 minutes per invoice process (manual data entry, validation, approvals)</p>
                  <p><strong>After:</strong> 30 seconds per invoice using IDP</p>
                  <p><strong>ROI:</strong> Up to 300%  through faster cycle times & reduced errors</p>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">Account Payable</CardTitle>
                <CardDescription className="text-base">
                  Streamline vendor invoice workflows with AI-Powered IDP, enabling fast, accurate validation and payment readiness.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Before:</strong> 2-3 hours per vendor payment cycle (manual verification, approvals, data entry)</p>
                  <p><strong>After:</strong> 15 minutes with IDP automation</p>
                  <p><strong>ROI:</strong> 85% reduction in processing time</p>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">Logistics & Supply Chain</CardTitle>
                <CardDescription className="text-base">
                  Streamline logistics workflows with specialized IDP solutions for Bill of Lading, e-Way Bills, and custom documents.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Before:</strong> Manual trade document entry, prone to delay & errors</p>
                  <p><strong>After:</strong> 100% automated document processing with AI-Powered IDP</p>
                  <p><strong>ROI:</strong> Reduced errors & faster clearance</p>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">Healthcare</CardTitle>
                <CardDescription className="text-base">
                  Leverage AI-Powered IDP to streamline healthcare workflows, from patient records to insurance claims processing.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Before:</strong> 1-2 days manual document review & validation</p>
                  <p><strong>After:</strong> 2 hours using IDP</p>
                  <p><strong>ROI:</strong> 400% efficiency gain</p>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">Utility Bill Management</CardTitle>
                <CardDescription className="text-base">
                  Streamline utility bill processing with AI-Powered data extraction and payment scheduling - build for speed, accuracy, and control.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Before:</strong> Manual bill entry, validation and payment tracking</p>
                  <p><strong>After:</strong> End-to-End automated utility bill processing with IDP</p>
                  <p><strong>ROI:</strong> 70% faster payment processing</p>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">KYC Customer Onboarding</CardTitle>
                <CardDescription className="text-base">
                  Accelerate customer onboarding with automated KYC document verification, identity validation and real-time data extraction.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Before:</strong> 5-7 days manual KYC verification & data entry</p>
                  <p><strong>After:</strong> 1 hour with IDP processing</p>
                  <p><strong>ROI:</strong> 90% faster customer activation</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-gradient-to-r from-teal-600 to-green-600 py-20">
          <div className="container mx-auto px-6 text-center">
            <div className="max-w-3xl mx-auto text-white">
              <h2 className="text-4xl font-bold mb-6">
                Ready to Automate Your Document Workflow with IDP?
              </h2>
              <p className="text-xl text-teal-100 mb-8">
                Experience the power of our IDP solution for seamless document processing, and intelligent data extraction.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg" asChild>
                  <a href="https://app.docsynecx.com/signin/" target="_blank" rel="noopener noreferrer">
                    Start Free Trial
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </a>
                </Button>
                <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg" asChild>
                  <Link to="/contact">
                    Talk to Experts
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      {/* Demo Request Form */}
      <DemoRequestForm />
      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Solutions;
