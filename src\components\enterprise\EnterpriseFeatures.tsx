import { <PERSON>T<PERSON>t, Zap, <PERSON>, <PERSON><PERSON><PERSON>, Lock, Globe } from "lucide-react";
import { <PERSON>, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";

const EnterpriseFeatures = () => {
  const features = [
    {
      icon: FileText,
      title: "Intelligent Document Processing",
      description: "Advanced AI that understands context, extracts data, and processes documents with human-level accuracy"
    },
    {
      icon: Zap,
      title: "Workflow Automation",
      description: "Create complex multi-step workflows that route, process, and validate documents automatically"
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Enable seamless collaboration with role-based access, approval workflows, and real-time updates"
    },
    {
      icon: BarChart,
      title: "Advanced Analytics",
      description: "Get deep insights into document processing performance, bottlenecks, and optimization opportunities"
    },
    {
      icon: Lock,
      title: "Enterprise Security",
      description: "Enterprise-grade security with SOC 2 compliance, encryption at rest and in transit, and audit trails"
    },
    {
      icon: Globe,
      title: "On-Premise Deployment",
      description: "Deploy across multiple regions with local data residency and compliance requirements"
    }
  ];

  return (
    <section id="enterprise-features" className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Enterprise-Grade Features
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Powerful capabilities designed for large-scale document processing operations
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-12 h-12 rounded-lg flex items-center justify-center mb-4 bg-green-100">
                  <feature.icon className="w-6 h-6 text-green-600" />
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
                <CardDescription className="text-base">
                  {feature.description}
                </CardDescription>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default EnterpriseFeatures;