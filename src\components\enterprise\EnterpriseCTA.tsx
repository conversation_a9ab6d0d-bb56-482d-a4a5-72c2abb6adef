import { Button } from "@/components/ui/button";
import { ArrowRight, Phone, Calendar } from "lucide-react";
import { useNavigate } from "react-router-dom";

const EnterpriseCTA = () => {
  const navigate = useNavigate();

  return (
    <section className="py-20 bg-gradient-to-r from-teal-600 to-green-600">
      <div className="container mx-auto px-6 text-center">
        <h2 className="text-4xl font-bold text-white mb-4">
          Ready to Transform Your Enterprise Operations?
        </h2>
        <p className="text-xl text-green-100 max-w-2xl mx-auto mb-8">
          Join leading enterprises who trust our Document AI Platform to automate
          their most critical business processes. Get a personalized demo and see
          how we can build workflows tailored to your specific needs.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Button size="lg" className="bg-white text-teal-700 hover:bg-green-50 group" onClick={() => {
            const form = document.getElementById('demo-request-form');
            if (form) form.scrollIntoView({ behavior: 'smooth' });
          }}>
            <Calendar className="w-4 h-4 mr-2" />
            Schedule Enterprise Demo
            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="border-white text-white bg-transparent hover:bg-white hover:text-teal-700 transition-colors"
            style={{ borderWidth: 2 }}
            onClick={() => navigate("/contact")}
          >
            <Phone className="w-4 h-4 mr-2" />
            <span className="font-semibold">Speak with Sales</span>
          </Button>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">97.0%</div>
            <div className="text-green-100">Processing Accuracy</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">75%</div>
            <div className="text-green-100">Faster Processing</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">24/7</div>
            <div className="text-green-100">Enterprise Support</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EnterpriseCTA;