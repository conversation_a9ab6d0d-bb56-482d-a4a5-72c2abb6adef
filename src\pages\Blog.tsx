import { <PERSON><PERSON><PERSON>, FileText, Clock, User, Tag, Calendar } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import Footer from "@/components/Footer";
import { blogPosts } from "@/data/blogData";

const Blog = () => {
  const navigate = useNavigate();

  // Set page title when component mounts
  useEffect(() => {
    document.title = "DocSynecX Latest Document Intelligence Insights";
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100">
      {/* Navigation */}
      <Navbar />

      {/* Hero Section */}
      <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight px-4">
            Latest Insights on{" "}
            <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
              Document AI
            </span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed px-4">
            Expert insights, best practices, and industry trends for document processing automation, and enterprise document AI integration
          </p>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            Latest Articles & Updates
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-4">
            Stay updated with the latest in document processing technologies, document AI, and enterprise integration best practices
          </p>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          {blogPosts.map((post, index) => (
            <Card key={post.id} className="hover:shadow-lg transition-shadow overflow-hidden cursor-pointer group" onClick={() => navigate(`/blog/${post.slug}`)}>
              <div className="relative h-48 sm:h-56 overflow-hidden">
                <img 
                  src={post.featuredImage}
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
              <CardHeader className="pb-4">
                <div className="flex items-center text-xs sm:text-sm text-gray-500 mb-2">
                  <Calendar className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                  <span>{post.date}</span>
                  <Clock className="w-3 h-3 sm:w-4 sm:h-4 ml-2 sm:ml-4 mr-1 sm:mr-2" />
                  <span>{post.readTime}</span>
                </div>
                <CardTitle className="text-lg sm:text-xl line-clamp-2 group-hover:text-teal-600 transition-colors">
                  {post.title}
                </CardTitle>
                <CardDescription className="line-clamp-3 text-sm sm:text-base">
                  {post.excerpt}
                </CardDescription>
                <div className="flex items-center mt-3 sm:mt-4">
                  <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gray-200 rounded-full flex items-center justify-center mr-2 sm:mr-3">
                    <User className="w-3 h-3 sm:w-4 sm:h-4" />
                  </div>
                  <span className="text-xs sm:text-sm text-gray-600">{post.author}</span>
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </section>

      {/* Demo Request Section */}
      <DemoRequestForm />

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-teal-600 to-green-600 py-12 sm:py-16 lg:py-20">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <div className="max-w-3xl mx-auto text-white">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6 px-4">
              Ready to Start Your Document AI Journey?
            </h2>
            <p className="text-lg sm:text-xl text-teal-100 mb-6 sm:mb-8 px-4">
              Join thousands of developers and businesses already using DocSynecX for intelligent document processing
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4">
              <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg w-full sm:w-auto">
                Start Free Trial
                <ArrowRight className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
              </Button>
              <Button size="lg" variant="outline" className="bg-white text-teal-600 hover:bg-gray-50 px-8 py-4 text-lg" onClick={() => navigate("/contact")}>
                Contact Sales Team
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Blog;
