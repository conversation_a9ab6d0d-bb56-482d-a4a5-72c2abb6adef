import { Code, Database, Zap, Download } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

const ResourcesApiDocs = () => {
  const resources = [
    {
      title: "OCR API Documentation",
      description: "Complete API reference for integrating OCR capabilities into your applications",
      type: "API Docs",
      icon: Code
    },
    {
      title: "Invoice Processing SDK",
      description: "Software development kit for automated invoice processing and data extraction",
      type: "SDK",
      icon: Database
    },
    {
      title: "ERP Integration Guide",
      description: "Step-by-step integration guides for SAP, Tally, Oracle, and other ERP systems",
      type: "Integration",
      icon: Zap
    },
    {
      title: "Webhooks & Events API",
      description: "Real-time notifications and event handling for document processing workflows",
      type: "API Docs",
      icon: Code
    }
  ];

  return (
    <section className="bg-white py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            API Documentation & Integration Guides
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Everything developers need to integrate DocSynecX into their applications
          </p>
        </div>
        
        <Tabs defaultValue="api-docs" className="w-full">
          <TabsList className="grid w-full grid-cols-3 max-w-2xl mx-auto mb-12">
            <TabsTrigger value="api-docs">API Documentation</TabsTrigger>
            <TabsTrigger value="integrations">ERP Integrations</TabsTrigger>
            <TabsTrigger value="sdks">SDKs & Tools</TabsTrigger>
          </TabsList>
          
          <TabsContent value="api-docs" className="space-y-8">
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
                      <Code className="w-5 h-5 text-teal-600" />
                    </div>
                    <CardTitle className="text-xl">OCR API Reference</CardTitle>
                  </div>
                  <CardDescription className="text-base">
                    Complete REST API documentation for OCR processing, including authentication, endpoints, and response formats
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm text-gray-600 mb-4">
                    <div className="flex justify-between">
                      <span>• Authentication & API Keys</span>
                      <span className="text-green-600">✓</span>
                    </div>
                    <div className="flex justify-between">
                      <span>• Document Upload Endpoints</span>
                      <span className="text-green-600">✓</span>
                    </div>
                    <div className="flex justify-between">
                      <span>• OCR Processing API</span>
                      <span className="text-green-600">✓</span>
                    </div>
                    <div className="flex justify-between">
                      <span>• Webhook Configuration</span>
                      <span className="text-green-600">✓</span>
                    </div>
                  </div>
                  <Button className="w-full">
                    <Download className="w-4 h-4 mr-2" />
                    View API Docs
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
                      <Database className="w-5 h-5 text-teal-600" />
                    </div>
                    <CardTitle className="text-xl">Invoice Processing API</CardTitle>
                  </div>
                  <CardDescription className="text-base">
                    Specialized APIs for automated invoice processing, data extraction, and workflow automation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm text-gray-600 mb-4">
                    <div className="flex justify-between">
                      <span>• Invoice Data Extraction</span>
                      <span className="text-green-600">✓</span>
                    </div>
                    <div className="flex justify-between">
                      <span>• Vendor & Line Item Parsing</span>
                      <span className="text-green-600">✓</span>
                    </div>
                    <div className="flex justify-between">
                      <span>• Validation & Approval Workflows</span>
                      <span className="text-green-600">✓</span>
                    </div>
                    <div className="flex justify-between">
                      <span>• Export to ERP Systems</span>
                      <span className="text-green-600">✓</span>
                    </div>
                  </div>
                  <Button className="w-full">
                    <Download className="w-4 h-4 mr-2" />
                    View Invoice API
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="integrations" className="space-y-8">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="w-16 h-16 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Database className="w-8 h-8 text-teal-600" />
                  </div>
                  <CardTitle className="text-lg">SAP Integration</CardTitle>
                  <CardDescription>
                    Complete integration guide for SAP S/4HANA, SAP ECC, and SAP Business One systems
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-gray-600 mb-4">
                    <li>• SAP RFC Connectivity</li>
                    <li>• Invoice to SAP FI Module</li>
                    <li>• Purchase Order Integration</li>
                    <li>• Real-time Data Sync</li>
                  </ul>
                  <Button variant="outline" className="w-full">View SAP Guide</Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Zap className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle className="text-lg">Tally Integration</CardTitle>
                  <CardDescription>
                    Seamless integration with Tally ERP 9 and TallyPrime for Indian businesses
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-gray-600 mb-4">
                    <li>• Tally XML Import/Export</li>
                    <li>• GST Invoice Processing</li>
                    <li>• Vendor Master Sync</li>
                    <li>• Automated Voucher Creation</li>
                  </ul>
                  <Button variant="outline" className="w-full">View Tally Guide</Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="w-16 h-16 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Code className="w-8 h-8 text-teal-600" />
                  </div>
                  <CardTitle className="text-lg">Oracle ERP Cloud</CardTitle>
                  <CardDescription>
                    Integration patterns for Oracle Fusion Cloud ERP and Oracle E-Business Suite
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-gray-600 mb-4">
                    <li>• Oracle REST APIs</li>
                    <li>• Fusion Middleware Integration</li>
                    <li>• Payables Module Sync</li>
                    <li>• Workflow Automation</li>
                  </ul>
                  <Button variant="outline" className="w-full">View Oracle Guide</Button>
                </CardContent>
              </Card>
            </div>

            <div className="bg-gradient-to-r from-teal-600 to-green-600 rounded-2xl p-8 text-white text-center">
              <h3 className="text-2xl font-bold mb-4">Custom ERP Integration</h3>
              <p className="text-teal-100 mb-6 max-w-2xl mx-auto">
                Don't see your ERP system listed? Our team specializes in custom integrations for any enterprise system. 
                We support legacy systems, custom-built ERPs, and specialized industry solutions.
              </p>
              <Button variant="outline" className="bg-white text-teal-600 hover:bg-gray-50">
                Contact Integration Team
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="sdks" className="space-y-8">
            <div className="grid md:grid-cols-2 gap-8">
              {resources.map((resource, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
                        <resource.icon className="w-5 h-5 text-teal-600" />
                      </div>
                      <div>
                        <CardTitle className="text-xl">{resource.title}</CardTitle>
                        <span className="text-sm text-teal-600 font-medium">{resource.type}</span>
                      </div>
                    </div>
                    <CardDescription className="text-base">
                      {resource.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      Download {resource.type}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
};

export default ResourcesApiDocs;
