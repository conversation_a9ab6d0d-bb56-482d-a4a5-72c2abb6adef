import { ArrowRight, FileText, Brain, Search, Shield, CheckCircle, Play, Calendar, Clock, User, Check, Star, Layers, Zap, Hash, Tag } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { blogPosts } from "@/data/blogData";
import DemoRequestForm from "@/components/platform/DemoRequestForm";

const Index = () => {
  const navigate = useNavigate();

  // Set page title and video volume when component mounts
  useEffect(() => {
    document.title = "DocSynecX - Intelligent Document Processing AI Platform";
    
    const video = document.getElementById('demo-video') as HTMLVideoElement;
    if (video) {
      video.volume = 0.2; // Set volume to 30%
    }
  }, []);

  const handleWatchDemo = () => {
    const videoSection = document.getElementById('ai-demo-video');
    const video = document.getElementById('demo-video') as HTMLVideoElement;

    if (videoSection) {
      videoSection.scrollIntoView({ behavior: 'smooth' });

      // Wait for scroll to complete, then play video
      setTimeout(() => {
        if (video) {
          video.volume = 0.3; // Set volume to 30%
          video.play().catch((error) => {
            console.log('Auto-play was prevented:', error);
            // Auto-play might be blocked by browser policy
            // User will need to click play manually
          });
        }
      }, 1000); // Wait 1 second for smooth scroll to complete
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-green-100">
      {/* Navigation */}
      <Navbar />

      {/* Hero Section */}
      <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
            Transform Your Workflow with{" "}
            <span className="bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
              AI-Powered Document Intelligence
            </span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed px-4">
            DocSynecX is the powerful intelligence platform that extracts data from documents and transform it into actionable insights- streamlining workflows and accelarating decisions
          </p>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4">
            <Button size="lg" className="bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg w-full sm:w-auto" asChild>
              <a href="https://app.docsynecx.com/signin/" target="_blank" rel="noopener noreferrer">
                Start Free Trial
                <ArrowRight className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
              </a>
            </Button>
            <Button size="lg" variant="outline" className="px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg w-full sm:w-auto" onClick={handleWatchDemo}>
              Watch AI Demo
            </Button>
          </div>
          <p className="text-sm text-gray-500 mt-4 px-4">
            No credit card required • 14-day free trial • 50 Pages free • Secure API access included
          </p>
        </div>
      </section>

      {/* Trust Section */}
      {/* <section className="container mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <div className="text-center mb-6 sm:mb-8">
          <p className="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base">Trusted by leading companies for document AI and OCR processing</p>
          <div className="flex flex-wrap justify-center items-center gap-4 sm:gap-8 opacity-60">
            <div className="text-lg sm:text-2xl font-bold text-gray-400">ACME Corp</div>
            <div className="text-lg sm:text-2xl font-bold text-gray-400">TechFlow</div>
            <div className="text-lg sm:text-2xl font-bold text-gray-400">DataPro</div>
            <div className="text-lg sm:text-2xl font-bold text-gray-400">InnovateLab</div>
          </div>
        </div>
      </section> */}

      {/* Workflow Video Section */}
      <section id="ai-demo-video" className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20 transition-all duration-300 rounded-lg">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            See Advanced Document AI processing in Action
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-4">
            Watch how our AI-powered document intelligence transforms complex invoice processing and document extraction into automated workflows
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="relative bg-gray-900 rounded-xl sm:rounded-2xl overflow-hidden shadow-2xl mx-4 sm:mx-0 transition-all duration-300 hover:shadow-3xl">
            <video
              id="demo-video"
              className="w-full aspect-video object-cover"
              // poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 450'%3E%3Cdefs%3E%3ClinearGradient id='grad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%234F46E5;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%237C3AED;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23grad)'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='white' font-size='24' font-weight='bold'%3EOCR Demo Video%3C/text%3E%3C/svg%3E"
              controls
              preload="metadata"
            >
              <source src="/IDP_Webpage.mp4" type="video/mp4" />
              <div className="aspect-video bg-gradient-to-br from-teal-600 to-green-600 flex items-center justify-center">
                <div className="text-center text-white px-4">
                  <div className="w-16 h-16 sm:w-20 sm:h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 hover:bg-white/30 transition-colors cursor-pointer">
                    <Play className="w-6 h-6 sm:w-8 sm:h-8 ml-1" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-semibold mb-2">OCR Document Processing Workflow</h3>
                  <p className="text-teal-100 text-sm sm:text-base">3 minutes • See intelligent document extraction</p>
                </div>
              </div>
            </video>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-8 sm:mt-12 px-4 sm:px-0">
            <div className="text-center">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-3 sm:mb-4">
                <span className="text-teal-600 font-bold text-base sm:text-lg">1</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2 text-sm sm:text-base">Upload & Document Scan</h4>
              <p className="text-gray-600 text-xs sm:text-sm">Upload PDFs, images - our document AI platform reads any document format</p>
            </div>
            <div className="text-center">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3 sm:mb-4">
                <span className="text-green-600 font-bold text-base sm:text-lg">2</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2 text-sm sm:text-base">AI Data Extraction</h4>
              <p className="text-gray-600 text-xs sm:text-sm">Advanced document AI extracts, parses data, and processes information automatically</p>
            </div>
            <div className="text-center sm:col-span-2 lg:col-span-1">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-3 sm:mb-4">
                <span className="text-teal-600 font-bold text-base sm:text-lg">3</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2 text-sm sm:text-base">Export Processed Data</h4>
              <p className="text-gray-600 text-xs sm:text-sm">Get structured data, processed information, and automated workflow results instantly</p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            AI-Powered Document Intelligence Features for Smarter Automation
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-4">
            Extract, classify, and understand content from any document - accurately and at scale.
          </p>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform">
                <Brain className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <CardTitle className="text-lg sm:text-xl">AI-Powered OCR Technology</CardTitle>
              <CardDescription className="text-sm sm:text-base leading-relaxed">
                Advanced optical character recognition with 97.0% accuracy. Our OCR API processes invoices, 
                extracts information from complex documents, and handles bill of lading OCR with intelligent data parsing.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform">
                <Tag className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <CardTitle className="text-lg sm:text-xl">Key-Value Pair Detection</CardTitle>
              <CardDescription className="text-sm sm:text-base leading-relaxed">
                Identify and extract important key-value pairs (eg., "Invoice No:12345") to simplify downstream processing.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform">
                <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <CardTitle className="text-lg sm:text-xl">Intelligent Data Validation</CardTitle>
              <CardDescription className="text-sm sm:text-base leading-relaxed">
                AI-powered validation for document processing and data parsing accuracy. Machine learning algorithms 
                detect anomalies in processed documents and suggest corrections for optimal document processing results.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform">
                <Layers className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <CardTitle className="text-lg sm:text-xl">Batch Document Processing</CardTitle>
              <CardDescription className="text-sm sm:text-base leading-relaxed">
                Process multiple documents simultaneously with high-volume batch processing capabilities. Upload hundreds of invoices,
                PDFs, and images for automated processing with intelligent queuing and parallel processing optimization.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform">
                <FileText className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <CardTitle className="text-lg sm:text-xl">Multi-Format Support</CardTitle>
              <CardDescription className="text-sm sm:text-base leading-relaxed">
                Online document platform supporting PDF, Image formats. Enables intelligence data extraction for invoices, bill of ladding and a wide range of document types with smart format recognition and seamless conversion.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform">
                <Zap className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <CardTitle className="text-lg sm:text-xl">Workflow & Integration Ready</CardTitle>
              <CardDescription className="text-sm sm:text-base leading-relaxed">
                Seamlessly connect to your ERP, SAP, Tally with API first calls.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="bg-gradient-to-r from-teal-600 to-green-600 py-12 sm:py-16 lg:py-20">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-3 sm:mb-4 px-4">
              Why Leading Companies Choose DocSynecX for Document Automation
            </h2>
            <p className="text-lg sm:text-xl text-teal-100 max-w-2xl mx-auto px-4">
              Join thousands of organizations transforming their document processing and invoice workflows with Document AI
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8">
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-white mb-2">10x</div>
              <div className="text-teal-100 text-base sm:text-lg">Faster Document Processing</div>
              <div className="text-teal-200 text-xs sm:text-sm mt-2">Reduce invoice processing time from hours to minutes with Document AI</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-white mb-2">97.0%</div>
              <div className="text-teal-100 text-base sm:text-lg">Document Accuracy Rate</div>
              <div className="text-teal-200 text-xs sm:text-sm mt-2">Industry-leading precision in data extraction</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-white mb-2">99.9%</div>
              <div className="text-teal-100 text-base sm:text-lg">API Uptime</div>
              <div className="text-teal-200 text-xs sm:text-sm mt-2">Reliable document processing with enterprise-grade infrastructure and 24/7 monitoring</div>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Section */}
      <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            Latest Document AI Insights
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-4">
            Stay on the latest in document intelligence, AI driven automation, and smart data extraction.
          </p>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-12">
          {blogPosts
            .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .slice(0, 3)
            .map((post) => (
            <Card key={post.id} className="hover:shadow-lg transition-shadow cursor-pointer group" onClick={() => navigate(`/blog/${post.slug}`)}>
              <div className="relative h-40 sm:h-48 overflow-hidden rounded-t-lg">
                <img
                  src={post.featuredImage}
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
              <CardHeader className="pb-4">
                <div className="flex items-center text-xs sm:text-sm text-gray-500 mb-2">
                  <Calendar className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                  <span>{post.date}</span>
                  <Clock className="w-3 h-3 sm:w-4 sm:h-4 ml-2 sm:ml-4 mr-1 sm:mr-2" />
                  <span>{post.readTime}</span>
                </div>
                <CardTitle className="text-lg sm:text-xl line-clamp-2 group-hover:text-teal-600 transition-colors">
                  {post.title}
                </CardTitle>
                <CardDescription className="line-clamp-3 text-sm sm:text-base">
                  {post.excerpt}
                </CardDescription>
                <div className="flex items-center mt-3 sm:mt-4">
                  <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gray-200 rounded-full flex items-center justify-center mr-2 sm:mr-3">
                    <User className="w-3 h-3 sm:w-4 sm:h-4" />
                  </div>
                  <span className="text-xs sm:text-sm text-gray-600">{post.author}</span>
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Link to="/blog" onClick={() => window.scrollTo(0, 0)}>
            <Button variant="outline" size="lg" className="px-6 sm:px-8 py-3 sm:py-3">
              View All Articles
              <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
          </Link>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4 sm:mb-6 px-4">
            Ready to Transform Your Document Processing with AI-Powered Intelligence?
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 px-4">
            Trusted by leading businesses for intelligent data extraction and end-to-end document automation.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4">
            <Button size="lg" className="bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg w-full sm:w-auto" asChild>
              <a href="https://app.docsynecx.com/signin/" target="_blank" rel="noopener noreferrer">
                Start Free Trial
                <ArrowRight className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
              </a>
            </Button>
            <Button size="lg" variant="outline" className="px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg w-full sm:w-auto" onClick={() => {
              const form = document.getElementById('demo-request-form');
              if (form) form.scrollIntoView({ behavior: 'smooth' });
            }}>
              Schedule Demo
            </Button>
          </div>
          <p className="text-xs sm:text-sm text-gray-500 mt-4 px-4">
            Questions about document AI? Contact our <NAME_EMAIL>
          </p>
        </div>
      </section> 

      {/* Demo Request Section */}
      <div id="demo-request-form">
        <DemoRequestForm />
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Index;



