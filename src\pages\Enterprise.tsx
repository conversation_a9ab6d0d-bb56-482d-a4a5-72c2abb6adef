import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import EnterpriseHero from "@/components/enterprise/EnterpriseHero";
import EnterpriseFeatures from "@/components/enterprise/EnterpriseFeatures";
import EnterpriseWorkflows from "@/components/enterprise/EnterpriseWorkflows";
import EnterpriseCTA from "@/components/enterprise/EnterpriseCTA";
import DemoRequestForm from "@/components/platform/DemoRequestForm";
import { useEffect } from "react";

const Enterprise = () => {
  useEffect(() => {
    document.title = "AI-Powered Document Automation for Enterprises - DocSynecX";
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-green-100 flex flex-col">
      <Navbar />
      <main className="flex-1">
        <EnterpriseHero />
        <EnterpriseFeatures />
        <EnterpriseWorkflows />
        <EnterpriseCTA />
        <DemoRequestForm />
      </main>
      <Footer />
    </div>
  );
};

export default Enterprise;