import { <PERSON>Text, <PERSON>u, X, Ch<PERSON>ronDown, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link, useLocation } from "react-router-dom";
import { useState } from "react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const Navbar = () => {
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);

  const toggleMenu = (menuId: string) => {
    setExpandedMenus(prev => 
      prev.includes(menuId) 
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  const navLinks = [
    { to: "/platform", label: "Platform" },
    { to: "/solutions", label: "Solutions" },
    { to: "/enterprise", label: "Enterprise" }, // <-- Added Enterprise after Solutions
    { to: "/pricing", label: "Pricing" },
    {
      to: "/resources",
      label: "Resources",
      dropdown: [
        { to: "/blog", label: "Blog" },
        // { to: "/resources", label: "API Documentation" },
        { to: "/help", label: "Help Center" },
      ],
    },
  ];

  const companyLinks = [
    { to: "/about", label: "About" },
    { to: "/contact", label: "Contact" },
   
  ];

  const isActive = (path: string) => location.pathname === path;
  const isCompanyActive = () => companyLinks.some(link => location.pathname === link.to);

  return (
    <nav className="container mx-auto px-4 sm:px-6 py-4 sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/50">
      <div className="flex items-center justify-between">
        {/* Left: Logo */}
        <Link to="/" className="flex items-center">
          <img src="/Docsynecx-Logo.svg" alt="Docsynecx Logo" className="h-10 w-auto" />
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex items-center justify-center flex-1 space-x-6 xl:space-x-8">
          {navLinks.map((link) =>
            !link.dropdown ? (
              <Link
                key={link.to}
                to={link.to}
                className={`text-base font-medium transition-colors px-2 py-1 ${
                  isActive(link.to)
                    ? "text-teal-600"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                {link.label}
              </Link>
            ) : (
              <DropdownMenu key={link.to}>
                <DropdownMenuTrigger asChild>
                  <button
                    className={`flex items-center text-base font-medium transition-colors px-2 py-1 ${
                      [link.to, ...link.dropdown.map(d => d.to)].includes(location.pathname)
                        ? "text-teal-600"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    {link.label}
                    <ChevronDown className="ml-1 h-4 w-4" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-52 mt-2 shadow-lg border border-gray-200 bg-white rounded-lg">
                  {link.dropdown.map((item) => (
                    <DropdownMenuItem key={item.to} asChild>
                      <Link
                        to={item.to}
                        className={`w-full px-4 py-3 text-base transition-colors rounded-md ${
                          isActive(item.to)
                            ? "text-teal-600 bg-teal-50"
                            : "text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                        }`}
                      >
                        {item.label}
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )
          )}

          {/* Company Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button
                className={`flex items-center text-base font-medium transition-colors px-2 py-1 ${
                  isCompanyActive()
                    ? "text-teal-600"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Company
                <ChevronDown className="ml-1 h-4 w-4" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-52 mt-2 shadow-lg border border-gray-200 bg-white rounded-lg">
              {companyLinks.map((link) => (
                <DropdownMenuItem key={link.to} asChild>
                  <Link
                    to={link.to}
                    className={`w-full px-4 py-3 text-base transition-colors rounded-md ${
                      isActive(link.to)
                        ? "text-teal-600 bg-teal-50"
                        : "text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                    }`}
                  >
                    {link.label}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Right side buttons */}
        <div className="hidden lg:flex items-center space-x-3">
            <Button
              variant="outline"
              size="default"
              className="border-gray-300 text-gray-700 hover:bg-gray-50 px-5 py-2.5 text-base"
              onClick={() => window.open("https://app.docsynecx.com/signin/", "_blank")}
            >
              Login
            </Button>
            <Button
              variant="outline"
              size="default"
              className="border-teal-600 text-teal-600 hover:bg-teal-50 px-5 py-2.5 text-base"
              onClick={() => window.open("https://app.docsynecx.com/signup/", "_blank")}
            >
              Sign Up
            </Button>
            <Button
              size="default"
              className="bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 text-white px-5 py-2.5 text-base"
              onClick={() => {
                const form = document.getElementById('demo-request-form');
                if (form) form.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              Book a Demo
            </Button>
        </div>

        {/* Mobile Navigation */}
        <div className="lg:hidden">
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="p-3">
                <Menu className="w-6 h-6" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[280px] sm:w-[320px] p-0">
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex items-center p-6 border-b border-gray-200">
                  <Link 
                    to="/" 
                    className="flex items-center space-x-2"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-teal-600 to-green-600 rounded-lg flex items-center justify-center">
                      <FileText className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xl font-bold bg-gradient-to-r from-teal-600 to-green-600 bg-clip-text text-transparent">
                      DocSynecX
                    </span>
                  </Link>
                </div>

                {/* Scrollable Navigation */}
                <nav className="flex-1 overflow-y-auto p-4">
                  <div className="space-y-2">
                    {navLinks.map((link) =>
                      !link.dropdown ? (
                        <Link
                          key={link.to}
                          to={link.to}
                          onClick={() => setIsOpen(false)}
                          className={`flex items-center px-4 py-3 text-base font-medium rounded-lg transition-colors ${
                            isActive(link.to)
                              ? "bg-teal-50 text-teal-600 border-l-4 border-teal-600"
                              : "text-gray-700 hover:bg-gray-50"
                          }`}
                        >
                          {link.label}
                        </Link>
                      ) : (
                        <div key={link.to} className="space-y-1">
                          <button
                            onClick={() => toggleMenu(link.to)}
                            className={`flex items-center justify-between w-full px-4 py-3 text-base font-medium rounded-lg transition-colors ${
                              [link.to, ...link.dropdown.map(d => d.to)].includes(location.pathname)
                                ? "bg-teal-50 text-teal-600"
                                : "text-gray-700 hover:bg-gray-50"
                            }`}
                          >
                            <span>{link.label}</span>
                            {expandedMenus.includes(link.to) ? (
                              <ChevronDown className="w-4 h-4" />
                            ) : (
                              <ChevronRight className="w-4 h-4" />
                            )}
                          </button>
                          {expandedMenus.includes(link.to) && (
                            <div className="ml-4 space-y-1">
                              {link.dropdown.map((item) => (
                                <Link
                                  key={item.to}
                                  to={item.to}
                                  onClick={() => setIsOpen(false)}
                                  className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                                    isActive(item.to)
                                      ? "bg-teal-100 text-teal-700 border-l-2 border-teal-600"
                                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                                  }`}
                                >
                                  <div className="w-2 h-2 bg-gray-400 rounded-full mr-3"></div>
                                  {item.label}
                                </Link>
                              ))}
                            </div>
                          )}
                        </div>
                      )
                    )}

                    {/* Company Section */}
                    <div className="pt-4 mt-4 border-t border-gray-200">
                      <button
                        onClick={() => toggleMenu('company')}
                        className={`flex items-center justify-between w-full px-4 py-3 text-base font-medium rounded-lg transition-colors ${
                          isCompanyActive()
                            ? "bg-teal-50 text-teal-600"
                            : "text-gray-700 hover:bg-gray-50"
                        }`}
                      >
                        <span>Company</span>
                        {expandedMenus.includes('company') ? (
                          <ChevronDown className="w-4 h-4" />
                        ) : (
                          <ChevronRight className="w-4 h-4" />
                        )}
                      </button>
                      {expandedMenus.includes('company') && (
                        <div className="ml-4 space-y-1 mt-2">
                          {companyLinks.map((link) => (
                            <Link
                              key={link.to}
                              to={link.to}
                              onClick={() => setIsOpen(false)}
                              className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                                isActive(link.to)
                                  ? "bg-teal-100 text-teal-700 border-l-2 border-teal-600"
                                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                              }`}
                            >
                              <div className="w-2 h-2 bg-gray-400 rounded-full mr-3"></div>
                              {link.label}
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </nav>

                {/* Bottom Actions */}
                <div className="border-t border-gray-200 p-4 space-y-3">
                  <Button
                    variant="outline"
                    className="w-full border-gray-300 text-gray-700 hover:bg-gray-50"
                    onClick={() => {
                      setIsOpen(false);
                      window.open("https://app.docsynecx.com/signin/", "_blank");
                    }}
                  >
                    Login
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full border-teal-600 text-teal-600 hover:bg-teal-50"
                    onClick={() => {
                      setIsOpen(false);
                      window.open("https://app.docsynecx.com/signup/", "_blank");
                    }}
                  >
                    Sign Up
                  </Button>
                  <Button
                    className="w-full bg-gradient-to-r from-teal-600 to-green-600 hover:from-teal-700 hover:to-green-700 text-white"
                    onClick={() => {
                      setIsOpen(false);
                      const form = document.getElementById('demo-request-form');
                      if (form) form.scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    Book a Demo
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;